{"ci": {"collect": {"url": ["https://digiclickai.netlify.app/", "https://digiclickai.netlify.app/cursor-context-demo", "https://digiclickai.netlify.app/portfolio", "https://digiclickai.netlify.app/about", "https://digiclickai.netlify.app/contact", "https://digiclickai.netlify.app/pricing"], "numberOfRuns": 3, "settings": {"chromeFlags": "--no-sandbox --disable-dev-shm-usage --disable-gpu", "preset": "desktop", "throttling": {"rttMs": 40, "throughputKbps": 10240, "cpuSlowdownMultiplier": 1, "requestLatencyMs": 0, "downloadThroughputKbps": 0, "uploadThroughputKbps": 0}, "emulatedFormFactor": "desktop", "locale": "en-US"}}, "assert": {"preset": "lighthouse:recommended", "assertions": {"categories:performance": ["error", {"minScore": 0.85}], "categories:accessibility": ["error", {"minScore": 0.95}], "categories:best-practices": ["error", {"minScore": 0.9}], "categories:seo": ["error", {"minScore": 0.9}], "first-contentful-paint": ["error", {"maxNumericValue": 2500}], "largest-contentful-paint": ["error", {"maxNumericValue": 4000}], "cumulative-layout-shift": ["error", {"maxNumericValue": 0.1}], "total-blocking-time": ["error", {"maxNumericValue": 300}], "speed-index": ["error", {"maxNumericValue": 3000}], "interactive": ["error", {"maxNumericValue": 5000}], "uses-responsive-images": "off", "offscreen-images": "warn", "render-blocking-resources": "warn", "unused-css-rules": "warn", "unused-javascript": "warn", "modern-image-formats": "warn", "uses-optimized-images": "warn", "uses-text-compression": "error", "uses-rel-preconnect": "warn", "font-display": "warn", "no-document-write": "error", "uses-http2": "warn", "uses-passive-event-listeners": "warn", "no-mutation-events": "error", "dom-size": ["warn", {"maxNumericValue": 1500}], "critical-request-chains": "warn", "user-timings": "off", "bootup-time": ["warn", {"maxNumericValue": 3000}], "mainthread-work-breakdown": ["warn", {"maxNumericValue": 4000}], "third-party-summary": "warn", "third-party-facades": "warn", "largest-contentful-paint-element": "off", "layout-shift-elements": "warn", "uses-long-cache-ttl": "warn", "total-byte-weight": ["warn", {"maxNumericValue": 2000000}], "redirects": "error", "image-aspect-ratio": "warn", "image-size-responsive": "warn", "preload-lcp-image": "warn", "valid-source-maps": "warn", "prioritize-lcp-image": "warn"}}, "upload": {"target": "temporary-public-storage", "reportFilenamePattern": "%%PATHNAME%%-%%DATETIME%%-report.%%EXTENSION%%"}, "server": {"port": 9001, "storage": "./lighthouse-reports"}}}