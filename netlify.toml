[build]
  command = "npm run build"
  publish = "out"

# Cache control for static assets
[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Cache control for images
[[headers]]
  for = "/*.{jpg,jpeg,png,gif,webp,svg,ico}"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Cache control for CSS and JavaScript
[[headers]]
  for = "/*.{css,js}"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Cache control for HTML and other dynamic content
[[headers]]
  for = "/*"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"

# Enable HTTP/2 Server Push
[[headers]]
  for = "/*"
  [headers.values]
    Link = '''
    </styles/Home.module.css>; rel=preload; as=style,
    </styles/visual-effects.css>; rel=preload; as=style,
    </styles/navigation-enhancements.css>; rel=preload; as=style'''

# Redirect all traffic to HTTPS
[[redirects]]
  from = "http://*"
  to = "https://:splat"
  status = 301
  force = true

# SPA fallback
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Enable brotli and gzip compression
[build.processing]
  skip_processing = false
[build.processing.css]
  bundle = true
  minify = true
[build.processing.js]
  bundle = true
  minify = true
[build.processing.html]
  pretty_urls = true
[build.processing.images]
  compress = true
