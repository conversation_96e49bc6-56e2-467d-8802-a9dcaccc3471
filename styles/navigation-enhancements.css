/* DigiClick AI Navigation Enhancement Styles */
/* Page transitions, loading states, and navigation UX improvements */

/* CSS Custom Properties for Navigation Control */
:root {
  /* Page Transition Controls */
  --page-transitions-enabled: 1;
  --transition-duration: 0.5s;
  --transition-variant: enhanced;
  
  /* Loading Animation Controls */
  --loading-animations-enabled: 1;
  --loading-spinner-size: 48px;
  --loading-overlay-opacity: 0.95;
  
  /* Breadcrumb Controls */
  --breadcrumb-display: flex;
  --breadcrumb-view-mode: full;
  
  /* Navigation Performance */
  --navigation-performance-mode: normal; /* normal, reduced, minimal */
}

/* Page Transition Overlay */
.page-transition-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #121212 0%, rgba(0, 212, 255, 0.1) 50%, #121212 100%);
  z-index: 9999;
  pointer-events: none;
  opacity: 0;
  visibility: hidden;
  backdrop-filter: blur(0px);
  transition: all var(--transition-duration) ease-out;
}

.page-transition-overlay.active {
  opacity: var(--loading-overlay-opacity);
  visibility: visible;
  backdrop-filter: blur(8px);
}

/* Page Transition Indicator */
.page-transition-indicator {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  text-align: center;
  color: #ffffff;
  font-family: 'Orbitron', monospace;
  transition: all 0.3s ease;
}

.page-transition-indicator.active {
  opacity: 1;
  visibility: visible;
}

.transition-spinner {
  width: var(--loading-spinner-size);
  height: var(--loading-spinner-size);
  margin: 0 auto 16px;
  position: relative;
}

.transition-spinner-svg {
  width: 100%;
  height: 100%;
  animation: transitionSpin 1s linear infinite;
  animation-play-state: running;
}

.transition-text {
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.9;
}

@keyframes transitionSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Route Loading Styles */
.route-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(18, 18, 18, var(--loading-overlay-opacity));
  backdrop-filter: blur(4px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.route-loading-content {
  text-align: center;
  color: #ffffff;
  font-family: 'Orbitron', monospace;
  max-width: 300px;
  padding: 32px;
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(123, 44, 191, 0.05));
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 16px;
  backdrop-filter: blur(8px);
}

.route-loading-spinner {
  width: 64px;
  height: 64px;
  margin: 0 auto 24px;
}

.circuit-spinner {
  width: 100%;
  height: 100%;
  animation: circuitRotate 2s linear infinite;
  animation-play-state: running;
}

.circuit-path {
  stroke-dasharray: 100;
  stroke-dashoffset: 100;
  animation: circuitFlow 1.5s ease-in-out infinite;
  animation-play-state: running;
}

.circuit-node {
  animation: circuitPulse 1.5s ease-in-out infinite;
  animation-play-state: running;
}

.route-loading-text {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.route-loading-progress {
  width: 100%;
  margin-top: 16px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00d4ff, #7b2cbf);
  border-radius: 2px;
  width: 0%;
  transition: width 0.3s ease-out;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progressShimmer 1.5s ease-in-out infinite;
  animation-play-state: running;
}

@keyframes circuitRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes circuitFlow {
  0%, 100% {
    stroke-dashoffset: 100;
    opacity: 0.3;
  }
  50% {
    stroke-dashoffset: 0;
    opacity: 1;
  }
}

@keyframes circuitPulse {
  0%, 100% {
    r: 2;
    opacity: 0.6;
  }
  50% {
    r: 4;
    opacity: 1;
  }
}

@keyframes progressShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Content Loading Styles */
.content-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
}

.content-loading-spinner {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
}

.neural-spinner {
  width: 100%;
  height: 100%;
  animation: neuralRotate 3s linear infinite;
  animation-play-state: running;
}

.neural-node {
  animation: neuralPulse 1.5s ease-in-out infinite;
  animation-play-state: running;
}

.neural-connection {
  stroke-dasharray: 20;
  stroke-dashoffset: 20;
  animation: neuralFlow 1.2s ease-in-out infinite;
  animation-play-state: running;
}

.content-loading-text {
  font-size: 14px;
  font-weight: 500;
  opacity: 0.9;
}

@keyframes neuralRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes neuralPulse {
  0%, 100% {
    r: 3;
    opacity: 0.6;
  }
  50% {
    r: 6;
    opacity: 1;
  }
}

@keyframes neuralFlow {
  0%, 100% {
    stroke-dashoffset: 20;
    opacity: 0.3;
  }
  50% {
    stroke-dashoffset: 0;
    opacity: 0.9;
  }
}

/* Form Loading Styles */
.form-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(18, 18, 18, 0.9);
  backdrop-filter: blur(2px);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: inherit;
}

.form-loading-content {
  text-align: center;
  color: #ffffff;
  font-family: 'Orbitron', monospace;
}

.form-loading-spinner {
  width: 40px;
  height: 40px;
  margin: 0 auto 12px;
}

.geometric-spinner {
  width: 100%;
  height: 100%;
  animation: geometricRotate 2s linear infinite;
  animation-play-state: running;
}

.geometric-shape {
  animation: geometricScale 1.5s ease-in-out infinite;
  animation-play-state: running;
  transform-origin: center;
}

.geometric-core {
  animation: geometricCorePulse 1.5s ease-in-out infinite;
  animation-play-state: running;
}

.form-loading-text {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

@keyframes geometricRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes geometricScale {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

@keyframes geometricCorePulse {
  0%, 100% {
    r: 6;
    opacity: 0.8;
  }
  50% {
    r: 10;
    opacity: 1;
  }
}

/* Error State Styles */
.error-state-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(18, 18, 18, 0.95);
  backdrop-filter: blur(8px);
  z-index: 9500;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.error-state-content {
  text-align: center;
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
  max-width: 400px;
  padding: 40px;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.05));
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 16px;
  backdrop-filter: blur(8px);
}

.error-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 24px;
  opacity: 0.8;
}

.error-message h3 {
  font-family: 'Orbitron', monospace;
  font-size: 20px;
  font-weight: 700;
  color: #ff6b6b;
  margin: 0 0 12px 0;
}

.error-message p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 32px 0;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.retry-button,
.home-button {
  padding: 12px 24px;
  border: 2px solid #00d4ff;
  border-radius: 8px;
  background: transparent;
  color: #00d4ff;
  font-family: 'Orbitron', monospace;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 44px; /* WCAG AA touch target */
  min-width: 44px;
}

.retry-button:hover,
.home-button:hover {
  background: #00d4ff;
  color: #121212;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 212, 255, 0.3);
}

.home-button {
  border-color: #7b2cbf;
  color: #7b2cbf;
}

.home-button:hover {
  background: #7b2cbf;
  color: #ffffff;
  box-shadow: 0 8px 20px rgba(123, 44, 191, 0.3);
}

/* Responsive Adjustments */
@media (max-width: 767px) {
  .route-loading-content {
    max-width: 280px;
    padding: 24px;
  }
  
  .route-loading-spinner {
    width: 48px;
    height: 48px;
  }
  
  .route-loading-text {
    font-size: 14px;
  }
  
  .error-state-content {
    max-width: 320px;
    padding: 32px 24px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .retry-button,
  .home-button {
    width: 100%;
    max-width: 200px;
  }
}

/* Performance Mode Adjustments */
.navigation-performance-minimal .route-loading-spinner,
.navigation-performance-minimal .content-loading-spinner,
.navigation-performance-minimal .form-loading-spinner {
  animation: none;
}

.navigation-performance-minimal .circuit-path,
.navigation-performance-minimal .circuit-node,
.navigation-performance-minimal .neural-node,
.navigation-performance-minimal .neural-connection,
.navigation-performance-minimal .geometric-shape,
.navigation-performance-minimal .geometric-core {
  animation: none;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .page-transition-overlay,
  .page-transition-indicator,
  .route-loading-overlay,
  .error-state-container {
    animation: none;
    transition: none;
  }
  
  .transition-spinner-svg,
  .circuit-spinner,
  .neural-spinner,
  .geometric-spinner,
  .circuit-path,
  .circuit-node,
  .neural-node,
  .neural-connection,
  .geometric-shape,
  .geometric-core,
  .progress-fill::after {
    animation: none;
  }
}

/* High Contrast Mode */
.high-contrast .route-loading-content,
.high-contrast .error-state-content {
  background: #000000;
  border-color: #ffffff;
}

.high-contrast .retry-button,
.high-contrast .home-button {
  border-color: #ffffff;
  color: #ffffff;
}

.high-contrast .retry-button:hover,
.high-contrast .home-button:hover {
  background: #ffffff;
  color: #000000;
}

/* Print Styles */
@media print {
  .page-transition-overlay,
  .page-transition-indicator,
  .route-loading-overlay,
  .content-loading-container,
  .form-loading-overlay,
  .error-state-container {
    display: none !important;
  }
}
