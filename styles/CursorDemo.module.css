/* Cursor Demo Page Styles */
.demo {
  min-height: 100vh;
  padding: 2rem;
  background: linear-gradient(135deg, #121212 0%, #1a1a1a 100%);
  color: #e0e0e0;
  font-family: 'Poppins', sans-serif;
}

/* Hero Section */
.hero {
  text-align: center;
  padding: 4rem 0;
  margin-bottom: 4rem;
}

.hero h1 {
  font-family: 'Orbitron', monospace;
  font-size: 3.5rem;
  margin-bottom: 1rem;
}

.subtitle {
  font-size: 1.2rem;
  color: #b0b0b0;
  max-width: 600px;
  margin: 0 auto;
}

/* Grid Layout */
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.gridItem {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 2rem;
  border: 1px solid rgba(0, 212, 255, 0.2);
  backdrop-filter: blur(10px);
}

.gridItem h2 {
  font-family: 'Orbitron', monospace;
  color: #00d4ff;
  margin-bottom: 1rem;
}

.gridItem p {
  color: #b0b0b0;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

/* Navigation */
.nav {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.nav a {
  color: #e0e0e0;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.nav a:hover {
  background: rgba(0, 212, 255, 0.1);
  border-color: rgba(0, 212, 255, 0.3);
}

/* Performance Section */
.performance {
  margin-bottom: 4rem;
  text-align: center;
}

.performance h2 {
  font-family: 'Orbitron', monospace;
  font-size: 2.5rem;
  margin-bottom: 2rem;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.features h3 {
  font-family: 'Orbitron', monospace;
  color: #00d4ff;
  margin-bottom: 1rem;
}

/* Trail Demo */
.trail {
  margin-bottom: 4rem;
  text-align: center;
}

.trail h2 {
  font-family: 'Orbitron', monospace;
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.trail p {
  color: #b0b0b0;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.trailArea {
  padding: 3rem;
  background: rgba(0, 212, 255, 0.02);
  border-radius: 20px;
  border: 1px solid rgba(0, 212, 255, 0.1);
}

/* Click Effects */
.clicks {
  margin-bottom: 4rem;
  text-align: center;
}

.clicks h2 {
  font-family: 'Orbitron', monospace;
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.clicks p {
  color: #b0b0b0;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.clickArea {
  padding: 3rem;
  background: rgba(123, 44, 191, 0.02);
  border-radius: 20px;
  border: 1px solid rgba(123, 44, 191, 0.1);
}

/* Theme Variations */
.themes {
  margin-bottom: 4rem;
  text-align: center;
}

.themes h2 {
  font-family: 'Orbitron', monospace;
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.themes p {
  color: #b0b0b0;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.themeGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.themeGrid h4 {
  font-family: 'Orbitron', monospace;
  color: #00d4ff;
  margin-bottom: 0.5rem;
}

/* Instructions */
.instructions {
  margin-bottom: 4rem;
  text-align: center;
}

.instructions h2 {
  font-family: 'Orbitron', monospace;
  font-size: 2.5rem;
  margin-bottom: 2rem;
}

.instructionGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.instructionGrid h4 {
  font-family: 'Orbitron', monospace;
  color: #00d4ff;
  margin-bottom: 1rem;
}

.instructionGrid p {
  color: #b0b0b0;
  line-height: 1.6;
}

/* Footer */
.footer {
  margin-top: 4rem;
  padding: 2rem 0;
}

.footer h3 {
  font-family: 'Orbitron', monospace;
  font-size: 2rem;
  margin-bottom: 1rem;
}

.footer p {
  color: #b0b0b0;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .demo {
    padding: 1rem;
  }

  .hero h1 {
    font-size: 2.5rem;
  }

  .grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .gridItem {
    padding: 1.5rem;
  }

  .features,
  .themeGrid,
  .instructionGrid {
    grid-template-columns: 1fr;
  }

  .nav {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .clickArea,
  .trailArea {
    padding: 2rem;
  }
}

@media (max-width: 480px) {
  .hero h1 {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .gridItem {
    padding: 1rem;
  }

  .performance h2,
  .trail h2,
  .clicks h2,
  .themes h2,
  .instructions h2 {
    font-size: 2rem;
  }
}

/* Animation Enhancements */
.gridItem {
  transition: all 0.3s ease;
}

.gridItem:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.2);
  border-color: rgba(0, 212, 255, 0.4);
}

/* Special Effects for Demo */
.trailArea:hover {
  background: rgba(0, 212, 255, 0.05);
  border-color: rgba(0, 212, 255, 0.3);
}

.clickArea:hover {
  background: rgba(123, 44, 191, 0.05);
  border-color: rgba(123, 44, 191, 0.3);
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .gridItem {
    transition: none;
  }
  
  .gridItem:hover {
    transform: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .demo {
    background: #000000;
    color: #ffffff;
  }

  .gridItem {
    background: #333333;
    border-color: #ffffff;
  }

  .nav a {
    border-color: #ffffff;
  }
}

/* Context-Aware Cursor Demo Styles */

.demoContainer {
  min-height: 100vh;
  background: #121212;
  color: #ffffff;
  padding: 2rem;
  font-family: 'Poppins', sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 4rem;
}

.header h1 {
  font-family: 'Orbitron', monospace;
  font-size: 3rem;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #00d4ff, #7b2cbf);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header p {
  font-size: 1.2rem;
  color: #cccccc;
  max-width: 600px;
  margin: 0 auto;
}

.section {
  margin-bottom: 4rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 1px solid rgba(0, 212, 255, 0.2);
  backdrop-filter: blur(10px);
}

.section h2 {
  font-family: 'Orbitron', monospace;
  color: #00d4ff;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

/* Button Styles */
.buttonGroup {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Navigation Demo */
.navDemo {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

/* Form Styles */
.form {
  max-width: 600px;
}

.inputGroup {
  margin-bottom: 1.5rem;
}

.inputGroup label {
  display: block;
  margin-bottom: 0.5rem;
  color: #00d4ff;
  font-weight: 600;
}

.inputGroup input,
.inputGroup textarea,
.inputGroup select {
  width: 100%;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 8px;
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.inputGroup input:focus,
.inputGroup textarea:focus,
.inputGroup select:focus {
  outline: none;
  border-color: #00d4ff;
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
}

.inputGroup input.error {
  border-color: #ff4444;
  box-shadow: 0 0 15px rgba(255, 68, 68, 0.3);
}

.errorText {
  color: #ff4444;
  font-size: 0.9rem;
  margin-top: 0.5rem;
  display: block;
}

/* Card Grid */
.cardGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

/* Draggable Elements */
.dragArea {
  display: flex;
  gap: 2rem;
  min-height: 200px;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 10px;
  border: 2px dashed rgba(0, 212, 255, 0.3);
}

.draggableItem {
  background: linear-gradient(45deg, #7b2cbf, #00d4ff);
  color: white;
  padding: 1.5rem;
  border-radius: 10px;
  cursor: none;
  user-select: none;
  font-weight: 600;
  text-align: center;
  min-width: 150px;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 212, 255, 0.2);
}

.draggableItem:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
}

/* Disabled Elements */
.disabledGroup {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Effects Grid */
.effectsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.effectsGrid > div {
  padding: 2rem;
  text-align: center;
  border-radius: 10px;
  font-weight: 600;
  cursor: none;
  transition: all 0.3s ease;
}

/* Performance Note */
.note {
  background: rgba(0, 255, 136, 0.05);
  border: 1px solid rgba(0, 255, 136, 0.2);
  border-radius: 10px;
  padding: 2rem;
}

.note h3 {
  color: #00ff88;
  margin-bottom: 1rem;
  font-family: 'Orbitron', monospace;
}

.note ul {
  list-style: none;
  padding: 0;
}

.note li {
  margin-bottom: 0.5rem;
  color: #cccccc;
}
