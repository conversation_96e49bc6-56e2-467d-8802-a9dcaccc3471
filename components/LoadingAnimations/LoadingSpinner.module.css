/* AI-Themed Loading Animations with Accessibility Support */

/* Loading Container */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-family: 'Orbitron', monospace;
}

.spinnerWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  will-change: transform;
}

/* Size Variants */
.small .spinnerWrapper {
  width: 24px;
  height: 24px;
}

.medium .spinnerWrapper {
  width: 48px;
  height: 48px;
}

.large .spinnerWrapper {
  width: 72px;
  height: 72px;
}

.extraLarge .spinnerWrapper {
  width: 96px;
  height: 96px;
}

/* Color Variants */
.primary {
  color: #00d4ff;
}

.secondary {
  color: #7b2cbf;
}

.accent {
  color: #a855f7;
}

/* Loading Text */
.loadingText {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  text-align: center;
  letter-spacing: 1px;
  text-transform: uppercase;
  opacity: 0.9;
  animation: textPulse 2s ease-in-out infinite;
  animation-play-state: var(--loading-animation-enabled, 1) > 0 ? running : paused;
}

@keyframes textPulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

/* Default Spinner */
.defaultSpinner {
  width: 100%;
  height: 100%;
  animation: spin var(--loading-speed, 1.5s) linear infinite;
  animation-play-state: var(--loading-animation-enabled, 1) > 0 ? running : paused;
}

.defaultCircle {
  transform-origin: center;
  animation: dashRotate calc(var(--loading-speed, 1.5s) * 2) ease-in-out infinite;
  animation-play-state: var(--loading-animation-enabled, 1) > 0 ? running : paused;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes dashRotate {
  0% {
    stroke-dasharray: 1 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90 200;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90 200;
    stroke-dashoffset: -125;
  }
}

/* Circuit Spinner */
.circuitSpinner {
  width: 100%;
  height: 100%;
  animation: circuitRotate calc(var(--loading-speed, 1.5s) * 2) linear infinite;
  animation-play-state: var(--loading-animation-enabled, 1) > 0 ? running : paused;
}

.circuitPath {
  stroke-dasharray: 100;
  stroke-dashoffset: 100;
  animation: circuitFlow calc(var(--loading-speed, 1.5s) * 1.5) ease-in-out infinite;
  animation-play-state: var(--loading-animation-enabled, 1) > 0 ? running : paused;
}

.circuitNode {
  animation: circuitPulse var(--loading-speed, 1.5s) ease-in-out infinite;
  animation-play-state: var(--loading-animation-enabled, 1) > 0 ? running : paused;
  filter: drop-shadow(0 0 4px currentColor);
}

@keyframes circuitRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes circuitFlow {
  0%, 100% {
    stroke-dashoffset: 100;
    opacity: 0.3;
  }
  50% {
    stroke-dashoffset: 0;
    opacity: 1;
  }
}

@keyframes circuitPulse {
  0%, 100% {
    r: 2;
    opacity: 0.6;
  }
  50% {
    r: 4;
    opacity: 1;
  }
}

/* Geometric Spinner */
.geometricSpinner {
  width: 100%;
  height: 100%;
  animation: geometricRotate calc(var(--loading-speed, 1.5s) * 2) linear infinite;
  animation-play-state: var(--loading-animation-enabled, 1) > 0 ? running : paused;
}

.geometricShape {
  animation: geometricScale var(--loading-speed, 1.5s) ease-in-out infinite;
  animation-play-state: var(--loading-animation-enabled, 1) > 0 ? running : paused;
  transform-origin: center;
}

.geometricCore {
  animation: geometricCorePulse var(--loading-speed, 1.5s) ease-in-out infinite;
  animation-play-state: var(--loading-animation-enabled, 1) > 0 ? running : paused;
  filter: drop-shadow(0 0 6px currentColor);
}

@keyframes geometricRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes geometricScale {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

@keyframes geometricCorePulse {
  0%, 100% {
    r: 6;
    opacity: 0.8;
  }
  50% {
    r: 10;
    opacity: 1;
  }
}

/* Neural Network Spinner */
.neuralSpinner {
  width: 100%;
  height: 100%;
  animation: neuralRotate calc(var(--loading-speed, 1.5s) * 3) linear infinite;
  animation-play-state: var(--loading-animation-enabled, 1) > 0 ? running : paused;
}

.neuralNode {
  animation: neuralPulse var(--loading-speed, 1.5s) ease-in-out infinite;
  animation-play-state: var(--loading-animation-enabled, 1) > 0 ? running : paused;
  filter: drop-shadow(0 0 3px currentColor);
}

.neuralConnection {
  stroke-dasharray: 20;
  stroke-dashoffset: 20;
  animation: neuralFlow calc(var(--loading-speed, 1.5s) * 1.2) ease-in-out infinite;
  animation-play-state: var(--loading-animation-enabled, 1) > 0 ? running : paused;
  opacity: 0.7;
}

@keyframes neuralRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes neuralPulse {
  0%, 100% {
    r: 3;
    opacity: 0.6;
  }
  50% {
    r: 6;
    opacity: 1;
  }
}

@keyframes neuralFlow {
  0%, 100% {
    stroke-dashoffset: 20;
    opacity: 0.3;
  }
  50% {
    stroke-dashoffset: 0;
    opacity: 0.9;
  }
}

/* Progress Spinner */
.progressSpinner {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progressSpinner svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.progressCircle {
  transition: stroke-dashoffset 0.3s ease-out;
  filter: drop-shadow(0 0 4px #00d4ff);
}

.progressText {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: 700;
  color: #00d4ff;
  text-shadow: 0 0 4px rgba(0, 212, 255, 0.5);
}

/* Loading Skeleton Styles */
.skeletonContainer {
  animation: skeletonPulse 2s ease-in-out infinite;
  animation-play-state: var(--loading-animation-enabled, 1) > 0 ? running : paused;
}

.skeletonLine,
.skeletonButton,
.skeletonImage,
.skeletonTitle,
.skeletonText,
.skeletonName,
.skeletonSubtext,
.skeletonCircle {
  background: linear-gradient(
    90deg,
    rgba(0, 212, 255, 0.1) 0%,
    rgba(123, 44, 191, 0.2) 50%,
    rgba(0, 212, 255, 0.1) 100%
  );
  background-size: 200% 100%;
  border-radius: 4px;
  animation: skeletonShimmer 2s ease-in-out infinite;
  animation-play-state: var(--loading-animation-enabled, 1) > 0 ? running : paused;
}

.skeletonText .skeletonLine {
  margin-bottom: 8px;
  height: 1rem;
}

.skeletonText .skeletonLine:last-child {
  margin-bottom: 0;
}

.skeletonCard {
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 12px;
  overflow: hidden;
  background: rgba(18, 18, 18, 0.8);
}

.skeletonImage {
  width: 100%;
  height: 200px;
  border-radius: 0;
}

.skeletonContent {
  padding: 16px;
}

.skeletonTitle {
  height: 24px;
  margin-bottom: 12px;
  border-radius: 4px;
}

.skeletonText {
  height: 16px;
  margin-bottom: 8px;
  border-radius: 4px;
}

.skeletonAvatar {
  display: flex;
  align-items: center;
  gap: 12px;
}

.skeletonCircle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  flex-shrink: 0;
}

.skeletonInfo {
  flex: 1;
}

.skeletonName {
  height: 18px;
  margin-bottom: 6px;
  border-radius: 4px;
}

.skeletonSubtext {
  height: 14px;
  width: 60%;
  border-radius: 4px;
}

.skeletonButton {
  border-radius: 8px;
  min-height: 40px;
}

@keyframes skeletonPulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

@keyframes skeletonShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Loading Button */
.loadingButton {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  border: 2px solid #00d4ff;
  border-radius: 8px;
  background: linear-gradient(45deg, rgba(0, 212, 255, 0.1), rgba(123, 44, 191, 0.1));
  color: #ffffff;
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease-out;
  overflow: hidden;
}

.loadingButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 212, 255, 0.3);
}

.loadingButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loadingButton.loading {
  pointer-events: none;
}

.buttonSpinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.buttonText {
  transition: opacity 0.3s ease-out;
}

.buttonTextHidden {
  opacity: 0;
  position: absolute;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .defaultSpinner,
  .circuitSpinner,
  .geometricSpinner,
  .neuralSpinner,
  .circuitPath,
  .circuitNode,
  .geometricShape,
  .geometricCore,
  .neuralNode,
  .neuralConnection,
  .skeletonContainer,
  .skeletonLine,
  .skeletonButton,
  .skeletonImage,
  .skeletonTitle,
  .skeletonText,
  .skeletonName,
  .skeletonSubtext,
  .skeletonCircle,
  .loadingText {
    animation: none;
  }
  
  .progressCircle {
    transition: none;
  }
}

/* Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
