/* DigiClick AI Breadcrumb Navigation Styles */
/* Integrates with existing glow animation system and responsive design */

.breadcrumb {
  padding: 16px 0;
  margin-bottom: 24px;
  border-bottom: 1px solid rgba(0, 212, 255, 0.1);
  background: linear-gradient(90deg, transparent 0%, rgba(0, 212, 255, 0.05) 50%, transparent 100%);
  position: relative;
}

.breadcrumb::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #00d4ff 50%, transparent 100%);
  opacity: 0.3;
}

.list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  margin: 0;
  padding: 0;
  list-style: none;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
}

.item {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.link {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  border-radius: 6px;
  transition: all 0.3s ease;
  position: relative;
  font-weight: 400;
  min-height: 32px; /* WCAG AA touch target */
  min-width: 32px;
}

.link:hover {
  color: #00d4ff;
  background: rgba(0, 212, 255, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2);
}

.link:focus {
  outline: 2px solid #00d4ff;
  outline-offset: 2px;
  color: #00d4ff;
  background: rgba(0, 212, 255, 0.15);
}

.link:active {
  transform: translateY(0);
  background: rgba(0, 212, 255, 0.2);
}

.current {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  color: #ffffff;
  font-weight: 600;
  background: linear-gradient(45deg, rgba(0, 212, 255, 0.2), rgba(123, 44, 191, 0.1));
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 6px;
  text-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
  box-shadow: 0 2px 8px rgba(0, 212, 255, 0.2);
}

.icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  stroke-width: 2;
}

.label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.separatorWrapper {
  display: flex;
  align-items: center;
  margin: 0 4px;
  opacity: 0.5;
}

.separator {
  width: 12px;
  height: 12px;
  color: rgba(255, 255, 255, 0.4);
  flex-shrink: 0;
}

.ellipsis {
  display: flex;
  align-items: center;
  padding: 6px 8px;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 600;
}

.ellipsisText {
  font-size: 16px;
  line-height: 1;
}

/* View Mode Variants */
.compact {
  padding: 12px 0;
  margin-bottom: 16px;
}

.compact .list {
  font-size: 13px;
  gap: 6px;
}

.compact .link,
.compact .current {
  padding: 4px 8px;
  min-height: 28px;
  min-width: 28px;
}

.compact .icon {
  width: 14px;
  height: 14px;
}

.compact .label {
  max-width: 120px;
}

.minimal .label {
  max-width: 80px;
}

.minimal .link,
.minimal .current {
  padding: 4px 6px;
}

/* Responsive Design */
@media (max-width: 767px) {
  .breadcrumb {
    padding: 12px 0;
    margin-bottom: 16px;
  }
  
  .list {
    font-size: 13px;
    gap: 4px;
  }
  
  .link,
  .current {
    padding: 8px 12px; /* Larger touch targets on mobile */
    min-height: 44px; /* WCAG AA mobile touch target */
    min-width: 44px;
  }
  
  .label {
    max-width: 100px;
  }
  
  .icon {
    width: 18px;
    height: 18px;
  }
  
  .separator {
    width: 14px;
    height: 14px;
  }
  
  /* Hide labels on very small screens, show icons only */
  @media (max-width: 480px) {
    .minimal .label:not(.current .label) {
      display: none;
    }
    
    .minimal .link {
      min-width: 44px;
      justify-content: center;
    }
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .breadcrumb {
    padding: 14px 0;
    margin-bottom: 20px;
  }
  
  .list {
    font-size: 14px;
  }
  
  .link,
  .current {
    min-height: 36px;
    min-width: 36px;
  }
  
  .label {
    max-width: 130px;
  }
}

@media (min-width: 1024px) {
  .breadcrumb {
    padding: 16px 0;
    margin-bottom: 24px;
  }
  
  .list {
    font-size: 15px;
  }
  
  .label {
    max-width: 200px;
  }
}

/* Animation Enhancements */
.link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0, 212, 255, 0.1), rgba(123, 44, 191, 0.05));
  border-radius: 6px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.link:hover::before {
  opacity: 1;
}

/* Glow Integration */
.link.glow-link {
  position: relative;
  overflow: hidden;
}

.link.glow-link::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.2), transparent);
  transition: left 0.5s ease;
  pointer-events: none;
}

.link.glow-link:hover::after {
  left: 100%;
}

.current.glow-text {
  animation: currentGlow 2s ease-in-out infinite alternate;
}

@keyframes currentGlow {
  from {
    box-shadow: 0 2px 8px rgba(0, 212, 255, 0.2);
    text-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
  }
  to {
    box-shadow: 0 4px 16px rgba(0, 212, 255, 0.4);
    text-shadow: 0 0 12px rgba(0, 212, 255, 0.8);
  }
}

/* High Contrast Mode */
.high-contrast .breadcrumb {
  background: #000000;
  border-bottom-color: #ffffff;
}

.high-contrast .link {
  color: #ffffff;
  border: 1px solid #ffffff;
}

.high-contrast .link:hover,
.high-contrast .link:focus {
  background: #ffffff;
  color: #000000;
}

.high-contrast .current {
  background: #ffffff;
  color: #000000;
  border: 2px solid #ffffff;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .link,
  .current,
  .link::before,
  .link::after {
    transition: none;
    animation: none;
  }
  
  .link:hover {
    transform: none;
  }
  
  .current.glow-text {
    animation: none;
  }
}

/* Print Styles */
@media print {
  .breadcrumb {
    background: none;
    border-bottom: 1px solid #000000;
    padding: 8px 0;
    margin-bottom: 16px;
  }
  
  .link,
  .current {
    color: #000000;
    background: none;
    box-shadow: none;
    text-shadow: none;
  }
  
  .separatorWrapper {
    opacity: 1;
  }
  
  .separator {
    color: #000000;
  }
}

/* Focus Management for Keyboard Navigation */
.breadcrumb:focus-within {
  outline: 2px solid #00d4ff;
  outline-offset: 4px;
  border-radius: 4px;
}

/* Screen Reader Only Content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Loading State */
.breadcrumb.loading {
  opacity: 0.6;
  pointer-events: none;
}

.breadcrumb.loading .link,
.breadcrumb.loading .current {
  background: rgba(255, 255, 255, 0.1);
  animation: breadcrumbPulse 1.5s ease-in-out infinite;
}

@keyframes breadcrumbPulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}
