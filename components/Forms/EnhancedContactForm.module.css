/* DigiClick AI Enhanced Contact Form Styles */
/* Integrates with existing glow animation system and responsive design */

.contactFormContainer {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px 20px;
  font-family: 'Poppins', sans-serif;
  color: #ffffff;
}

.formHeader {
  text-align: center;
  margin-bottom: 40px;
}

.formTitle {
  font-family: 'Orbitron', monospace;
  font-size: 32px;
  font-weight: 700;
  color: #00d4ff;
  margin: 0 0 16px 0;
  text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
  background: linear-gradient(45deg, #00d4ff, #7b2cbf);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.formSubtitle {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  line-height: 1.6;
}

.contactForm {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.05), rgba(123, 44, 191, 0.02));
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 16px;
  padding: 40px;
  backdrop-filter: blur(10px);
  position: relative;
}

.contactForm::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.1), transparent);
  border-radius: 16px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.contactForm:focus-within::before {
  opacity: 1;
}

.formGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-bottom: 32px;
}

.fieldGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
}

.fieldGroup.fullWidth {
  grid-column: 1 / -1;
}

.fieldLabel {
  font-weight: 600;
  font-size: 14px;
  color: #ffffff;
  margin-bottom: 4px;
  font-family: 'Orbitron', monospace;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.fieldInput,
.fieldTextarea,
.fieldSelect {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 16px;
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
  transition: all 0.3s ease;
  min-height: 44px; /* WCAG AA touch target */
  position: relative;
}

.fieldInput:focus,
.fieldTextarea:focus,
.fieldSelect:focus {
  outline: none;
  border-color: #00d4ff;
  box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.3), 0 0 12px rgba(0, 212, 255, 0.2);
  background: rgba(0, 212, 255, 0.05);
}

.fieldInput::placeholder,
.fieldTextarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.fieldTextarea {
  resize: vertical;
  min-height: 120px;
  line-height: 1.6;
}

.fieldSelect {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%2300d4ff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

.fieldHelp {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.4;
  margin-top: 4px;
}

/* Validation States */
.fieldInput.field-valid,
.fieldTextarea.field-valid,
.fieldSelect.field-valid {
  border-color: #00d4ff;
  box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.3);
}

.fieldInput.field-invalid,
.fieldTextarea.field-invalid,
.fieldSelect.field-invalid {
  border-color: #ff6b6b;
  box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.3);
}

.fieldError {
  color: #ff6b6b;
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.fieldError::before {
  content: '⚠';
  font-size: 14px;
}

.fieldSuccess {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #00d4ff;
  pointer-events: none;
}

/* Checkbox Styling */
.checkboxGroup {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-top: 8px;
}

.fieldCheckbox {
  width: 20px;
  height: 20px;
  margin: 0;
  accent-color: #00d4ff;
  cursor: pointer;
  flex-shrink: 0;
  margin-top: 2px;
}

.checkboxLabel {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  cursor: pointer;
  font-family: 'Poppins', sans-serif;
  font-weight: normal;
  text-transform: none;
  letter-spacing: normal;
}

.checkboxLabel a {
  color: #00d4ff;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.3s ease;
}

.checkboxLabel a:hover {
  border-bottom-color: #00d4ff;
}

/* File Upload Container */
.fileUploadContainer {
  margin-top: 8px;
}

/* Submit Section */
.submitSection {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.submitButton {
  background: linear-gradient(45deg, #00d4ff, #7b2cbf);
  border: none;
  border-radius: 50px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  font-family: 'Orbitron', monospace;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  min-height: 56px;
  min-width: 200px;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.submitButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.submitButton:hover::before {
  left: 100%;
}

.submitButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
}

.submitButton:focus {
  outline: 2px solid #00d4ff;
  outline-offset: 2px;
}

.submitButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.submitSpinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.submitHelp {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 12px;
  line-height: 1.4;
}

/* Auto-save Status */
.saveStatusIndicator {
  position: absolute;
  top: -40px;
  right: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 6px;
  font-size: 12px;
  color: #00d4ff;
  font-family: 'Poppins', sans-serif;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.saveStatusIndicator.saving {
  background: rgba(255, 193, 7, 0.1);
  border-color: rgba(255, 193, 7, 0.2);
  color: #ffc107;
}

.saveStatusIndicator.error {
  background: rgba(255, 107, 107, 0.1);
  border-color: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
}

/* Form Messages */
.formSuccessMessage,
.formErrorMessage {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  margin-top: 16px;
  animation: slideIn 0.3s ease-out;
}

.formSuccessMessage {
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  color: #00d4ff;
}

.formErrorMessage {
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  color: #ff6b6b;
}

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

/* Responsive Design */
@media (max-width: 767px) {
  .contactFormContainer {
    padding: 24px 16px;
  }
  
  .formTitle {
    font-size: 24px;
  }
  
  .formSubtitle {
    font-size: 16px;
  }
  
  .contactForm {
    padding: 24px;
  }
  
  .formGrid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .fieldInput,
  .fieldTextarea,
  .fieldSelect {
    font-size: 16px; /* Prevent iOS zoom */
    padding: 14px 16px;
  }
  
  .submitButton {
    width: 100%;
    padding: 18px 32px;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .contactFormContainer {
    padding: 32px 20px;
  }
  
  .contactForm {
    padding: 32px;
  }
  
  .formGrid {
    gap: 20px;
  }
}

/* High Contrast Mode */
.high-contrast .contactForm {
  background: #000000;
  border-color: #ffffff;
}

.high-contrast .fieldInput,
.high-contrast .fieldTextarea,
.high-contrast .fieldSelect {
  background: #000000;
  border-color: #ffffff;
  color: #ffffff;
}

.high-contrast .submitButton {
  background: #ffffff;
  color: #000000;
  border: 2px solid #ffffff;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .contactForm::before,
  .submitButton::before,
  .submitSpinner,
  .formSuccessMessage,
  .formErrorMessage {
    animation: none;
    transition: none;
  }
  
  .submitButton:hover {
    transform: none;
  }
}

/* Print Styles */
@media print {
  .contactForm {
    background: white;
    color: black;
    border: 1px solid black;
  }
  
  .fieldInput,
  .fieldTextarea,
  .fieldSelect {
    background: white;
    color: black;
    border: 1px solid black;
  }
  
  .submitButton {
    background: white;
    color: black;
    border: 2px solid black;
  }
}
