/* Visual Effects Panel Styles - Extends CursorCustomizationPanel styles */
@import './CursorCustomizationPanel.module.css';

/* Performance Mode Grid */
.performanceGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.performanceOption {
  cursor: pointer;
  transition: all 0.3s ease;
}

.performanceOption input[type="radio"] {
  display: none;
}

.performanceCard {
  padding: 20px;
  border: 2px solid #333333;
  border-radius: 16px;
  background: linear-gradient(135deg, rgba(18, 18, 18, 0.9), rgba(42, 42, 42, 0.9));
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.performanceCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00d4ff, transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.performanceOption:hover .performanceCard {
  border-color: rgba(0, 212, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.performanceOption:hover .performanceCard::before {
  opacity: 0.6;
}

.performanceOption input[type="radio"]:checked + .performanceCard {
  border-color: #00d4ff;
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(123, 44, 191, 0.05));
  box-shadow: 
    0 0 20px rgba(0, 212, 255, 0.3),
    0 8px 25px rgba(0, 0, 0, 0.4);
  transform: translateY(-4px);
}

.performanceOption input[type="radio"]:checked + .performanceCard::before {
  opacity: 1;
}

.performanceHeader {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.performanceIcon {
  font-size: 24px;
  filter: drop-shadow(0 0 4px rgba(0, 212, 255, 0.5));
  animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 4px rgba(0, 212, 255, 0.5));
  }
  50% {
    transform: scale(1.1);
    filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.8));
  }
}

.performanceLabel {
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  font-size: 16px;
  color: #ffffff;
  text-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
}

.performanceDescription {
  font-family: 'Poppins', sans-serif;
  font-size: 13px;
  color: #cccccc;
  margin: 0;
  line-height: 1.5;
  opacity: 0.9;
}

/* Demo Area Styles */
.demoArea {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 24px;
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.05), rgba(123, 44, 191, 0.03));
  border-radius: 16px;
  border: 1px solid rgba(0, 212, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.demoArea::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #00d4ff, transparent);
  animation: demoGlow 3s ease-in-out infinite;
}

@keyframes demoGlow {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
}

.demoButton {
  padding: 14px 28px;
  border: 2px solid #00d4ff;
  border-radius: 12px;
  background: linear-gradient(45deg, rgba(0, 212, 255, 0.1), rgba(123, 44, 191, 0.1));
  color: #ffffff;
  text-align: center;
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
}

.demoButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.demoButton:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 
    0 0 20px rgba(0, 212, 255, 0.4),
    0 8px 25px rgba(0, 0, 0, 0.3);
  border-color: #7b2cbf;
}

.demoButton:hover::before {
  left: 100%;
}

.demoHeading {
  font-family: 'Orbitron', monospace;
  font-size: 20px;
  font-weight: 700;
  margin: 0;
  text-align: center;
  background: linear-gradient(45deg, #00d4ff, #7b2cbf, #a855f7);
  background-size: 200% 200%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: holographicDemo 3s ease-in-out infinite;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

@keyframes holographicDemo {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.demoCard {
  padding: 20px;
  background: linear-gradient(135deg, rgba(18, 18, 18, 0.9), rgba(42, 42, 42, 0.9));
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 12px;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.demoCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #00d4ff, transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.demoCard:hover {
  transform: translateY(-2px);
  border-color: #00d4ff;
  box-shadow: 
    0 0 20px rgba(0, 212, 255, 0.3),
    0 8px 25px rgba(0, 0, 0, 0.3);
}

.demoCard:hover::before {
  opacity: 0.8;
}

.demoCard p {
  margin: 0;
  font-family: 'Poppins', sans-serif;
  color: #ffffff;
  font-size: 14px;
  opacity: 0.9;
}

/* Setting Group Enhancements */
.settingGroup {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.sliderLabel {
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background: rgba(0, 212, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(0, 212, 255, 0.1);
  transition: all 0.3s ease;
}

.sliderLabel:hover {
  background: rgba(0, 212, 255, 0.1);
  border-color: rgba(0, 212, 255, 0.3);
}

/* Enhanced Toggle Styles */
.toggle {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
  background: rgba(18, 18, 18, 0.5);
  border: 1px solid rgba(0, 212, 255, 0.1);
}

.toggle:hover {
  background: rgba(0, 212, 255, 0.1);
  border-color: rgba(0, 212, 255, 0.3);
  transform: translateY(-1px);
}

.toggleLabel small {
  font-size: 12px;
  color: #cccccc;
  opacity: 0.8;
  margin-top: 4px;
  line-height: 1.3;
}

/* Preview Controls Enhancement */
.previewControls {
  padding: 20px;
  background: linear-gradient(135deg, rgba(123, 44, 191, 0.1), rgba(0, 212, 255, 0.05));
  border-radius: 12px;
  border: 1px solid rgba(123, 44, 191, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .performanceGrid {
    grid-template-columns: 1fr;
  }
  
  .demoArea {
    padding: 16px;
  }
  
  .demoButton {
    padding: 12px 20px;
    font-size: 12px;
  }
  
  .demoHeading {
    font-size: 16px;
  }
}

/* High Contrast Mode Support */
.high-contrast .performanceCard {
  background: #000000;
  border-color: #ffffff;
}

.high-contrast .demoArea {
  background: #000000;
  border-color: #ffffff;
}

.high-contrast .demoButton {
  background: #000000;
  border-color: #ffffff;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .performanceIcon,
  .demoArea::before,
  .demoHeading {
    animation: none;
  }
  
  .performanceCard,
  .demoButton,
  .demoCard,
  .toggle,
  .sliderLabel {
    transition: none;
  }
  
  .performanceOption:hover .performanceCard,
  .demoButton:hover,
  .demoCard:hover,
  .toggle:hover {
    transform: none;
  }
}
