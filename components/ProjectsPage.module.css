.projects {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'Arial', sans-serif;
  text-align: center;
}

.title {
  font-size: 3rem;
  font-weight: 900;
  margin-bottom: 2rem;
}

.projectsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2rem;
}

.projectCard {
  border: 1px solid #eee;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
  background: #fff;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s ease;
}

.projectCard:hover {
  transform: translateY(-5px);
}

.projectImage {
  width: 100%;
  height: 180px;
  object-fit: cover;
}

.projectTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 1rem;
  color: #121212;
}

.projectDescription {
  flex: 1;
  margin: 0 1rem 1rem 1rem;
  color: #444;
}

.projectLink {
  margin: 0 1rem 1rem 1rem;
  color: #7b2cbf;
  font-weight: 600;
  text-decoration: none;
  align-self: flex-start;
  transition: color 0.3s ease;
}

.projectLink:hover {
  color: #5a1a9e;
}
