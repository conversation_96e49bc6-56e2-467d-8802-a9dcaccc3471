/* DigiClick AI Loading Component Styles */

.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  min-height: 200px;
}

.fullScreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background: linear-gradient(135deg, #121212 0%, #1a1a1a 100%);
}

.dark {
  background: linear-gradient(135deg, #121212 0%, #1a1a1a 100%);
  color: #00d4ff;
}

.light {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  color: #7b2cbf;
}

.loadingContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 400px;
  width: 100%;
}

.logoContainer {
  margin-bottom: 2rem;
  opacity: 0;
  animation: fadeInUp 0.8s ease-out forwards;
}

.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.logoIcon {
  position: relative;
}

.logoSvg {
  color: #00d4ff;
  filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.3));
  animation: pulse 2s ease-in-out infinite;
}

.logoCircle {
  stroke-dasharray: 157;
  stroke-dashoffset: 157;
  animation: drawCircle 2s ease-out forwards;
}

.logoCheck {
  stroke-dasharray: 50;
  stroke-dashoffset: 50;
  animation: drawCheck 1s ease-out 1.5s forwards;
}

.logoText {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
}

.logoMain {
  font-family: 'Orbitron', monospace;
  font-size: 1.5rem;
  font-weight: 700;
  color: #00d4ff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.logoSub {
  font-family: 'Orbitron', monospace;
  font-size: 1rem;
  font-weight: 400;
  color: #7b2cbf;
  text-shadow: 0 0 8px rgba(123, 44, 191, 0.5);
}

.loadingAnimation {
  margin-bottom: 2rem;
  opacity: 0;
  animation: fadeInUp 0.8s ease-out 0.3s forwards;
}

.spinner {
  position: relative;
  width: 60px;
  height: 60px;
}

.spinnerRing {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid transparent;
  border-radius: 50%;
  animation: spin 2s linear infinite;
}

.spinnerRing:nth-child(1) {
  border-top-color: #00d4ff;
  animation-duration: 2s;
}

.spinnerRing:nth-child(2) {
  border-right-color: #7b2cbf;
  animation-duration: 1.5s;
  animation-direction: reverse;
}

.spinnerRing:nth-child(3) {
  border-bottom-color: #a855f7;
  animation-duration: 1s;
}

.loadingText {
  margin-bottom: 2rem;
  opacity: 0;
  animation: fadeInUp 0.8s ease-out 0.6s forwards;
}

.message {
  font-family: 'Poppins', sans-serif;
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 1rem;
  color: inherit;
  animation: textGlow 2s ease-in-out infinite alternate;
}

.progressContainer {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
  max-width: 300px;
}

.progressBar {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #00d4ff 0%, #7b2cbf 50%, #a855f7 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
  position: relative;
}

.progressFill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: shimmer 1.5s ease-in-out infinite;
}

.progressText {
  font-family: 'Orbitron', monospace;
  font-size: 0.9rem;
  font-weight: 600;
  color: #00d4ff;
  min-width: 40px;
  text-align: right;
}

.loadingDots {
  display: flex;
  gap: 0.5rem;
  opacity: 0;
  animation: fadeInUp 0.8s ease-out 0.9s forwards;
}

.dot {
  width: 8px;
  height: 8px;
  background: #00d4ff;
  border-radius: 50%;
  animation: bounce 1.4s ease-in-out infinite both;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

.dot:nth-child(3) {
  animation-delay: 0s;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.3));
  }
  50% {
    transform: scale(1.05);
    filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.6));
  }
}

@keyframes drawCircle {
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes drawCheck {
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes textGlow {
  from {
    text-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
  }
  to {
    text-shadow: 0 0 15px rgba(0, 212, 255, 0.6);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .loadingContainer {
    padding: 1rem;
  }
  
  .logoMain {
    font-size: 1.25rem;
  }
  
  .logoSub {
    font-size: 0.9rem;
  }
  
  .message {
    font-size: 1rem;
  }
  
  .spinner {
    width: 50px;
    height: 50px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .logoSvg,
  .spinnerRing,
  .dot,
  .progressFill::after {
    animation: none;
  }
  
  .logoCircle,
  .logoCheck {
    stroke-dashoffset: 0;
  }
  
  .loadingContainer,
  .loadingAnimation,
  .loadingText,
  .loadingDots {
    animation: none;
    opacity: 1;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .dark {
    background: #000000;
    color: #ffffff;
  }
  
  .logoSvg,
  .logoMain,
  .progressText {
    color: #ffffff;
  }
  
  .logoSub {
    color: #cccccc;
  }
  
  .progressFill {
    background: #ffffff;
  }
  
  .dot {
    background: #ffffff;
  }
}

/* Screen reader only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
