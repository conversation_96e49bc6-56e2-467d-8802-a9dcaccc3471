/* DigiClick AI Header Styles */

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(18, 18, 18, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 212, 255, 0.1);
  transition: all 0.3s ease;
  padding: 1rem 0;
}

.header.scrolled {
  background: rgba(18, 18, 18, 0.98);
  padding: 0.5rem 0;
  box-shadow: 0 4px 20px rgba(0, 212, 255, 0.1);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Logo */
.logo {
  font-family: 'Orbitron', sans-serif;
  font-size: 1.5rem;
  font-weight: 700;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: all 0.3s ease;
}

/* Navigation */
.nav {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.navLink {
  color: #e0e0e0;
  text-decoration: none;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.navLink:hover {
  color: #00d4ff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.navLink::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #00d4ff, #7b2cbf);
  transition: width 0.3s ease;
}

.navLink:hover::after {
  width: 100%;
}

/* Mobile Menu Button */
.mobileMenuButton {
  display: none;
  background: none;
  border: none;
  color: #00d4ff;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  transition: all 0.3s ease;
}

.mobileMenuButton:hover {
  color: #7b2cbf;
  transform: scale(1.1);
}

/* Mobile Menu */
.mobileMenu {
  position: fixed;
  top: 0;
  right: -100%;
  width: 300px;
  height: 100vh;
  background: rgba(18, 18, 18, 0.98);
  backdrop-filter: blur(20px);
  border-left: 1px solid rgba(0, 212, 255, 0.2);
  transition: right 0.3s ease;
  z-index: 1001;
  overflow-y: auto;
  padding: 2rem 0;
}

.mobileMenu.open {
  right: 0;
}

.closeButton {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: #00d4ff;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  transition: all 0.3s ease;
}

.closeButton:hover {
  color: #7b2cbf;
  transform: rotate(90deg);
}

.mobileNavLinks {
  list-style: none;
  padding: 0;
  margin: 3rem 0 0 0;
}

.mobileNavLinks li {
  margin: 0;
}

.mobileNavLinks a {
  display: block;
  color: #e0e0e0;
  text-decoration: none;
  padding: 1rem 2rem;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.mobileNavLinks a:hover {
  color: #00d4ff;
  background: rgba(0, 212, 255, 0.1);
  border-left-color: #00d4ff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.mobileNavDivider {
  color: #7b2cbf;
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  padding: 1.5rem 2rem 0.5rem;
  border-top: 1px solid rgba(0, 212, 255, 0.1);
  margin-top: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav {
    display: none;
  }

  .mobileMenuButton {
    display: block;
  }

  .container {
    padding: 0 1rem;
  }
}

.logoText {
  color: #00d4ff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.logoAccent {
  color: #7b2cbf;
  text-shadow: 0 0 10px rgba(123, 44, 191, 0.5);
}

.logo:hover {
  transform: scale(1.05);
}

/* Desktop Navigation */
.desktopNav {
  display: flex;
  align-items: center;
}

.navList {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.navLink {
  color: #e0e0e0;
  text-decoration: none;
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  font-size: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
}

.navLink:hover {
  color: #00d4ff;
  background: rgba(0, 212, 255, 0.1);
  transform: translateY(-2px);
}

.navLink.active {
  color: #00d4ff;
  background: rgba(0, 212, 255, 0.15);
}

.navLink::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #00d4ff, #7b2cbf);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.navLink:hover::after,
.navLink.active::after {
  width: 80%;
}

/* CTA Button */
.ctaContainer {
  display: flex;
  align-items: center;
}

.ctaButton {
  background: linear-gradient(45deg, #00d4ff, #7b2cbf);
  color: white;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-family: 'Orbitron', sans-serif;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

.ctaButton:hover {
  transform: scale(1.05) translateY(-2px);
  box-shadow: 0 8px 25px rgba(123, 44, 191, 0.4);
}
/* Mobile Menu Button */
.mobileMenuButton {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 1001;
}

.hamburgerLine {
  width: 25px;
  height: 3px;
  background: #00d4ff;
  margin: 3px 0;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.mobileMenuButton.open .hamburgerLine:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobileMenuButton.open .hamburgerLine:nth-child(2) {
  opacity: 0;
}

.mobileMenuButton.open .hamburgerLine:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Navigation */
.mobileNav {
  position: fixed;
  top: 0;
  right: -100%;
  width: 300px;
  height: 100vh;
  background: rgba(18, 18, 18, 0.98);
  backdrop-filter: blur(20px);
  transition: right 0.3s ease;
  z-index: 999;
  border-left: 1px solid rgba(0, 212, 255, 0.2);
}

.mobileNav.open {
  right: 0;
}

.mobileNavContent {
  padding: 6rem 2rem 2rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.mobileNavList {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.mobileNavLink {
  color: #e0e0e0;
  text-decoration: none;
  font-family: 'Poppins', sans-serif;
  font-size: 1.1rem;
  padding: 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: block;
}

.mobileNavLink:hover,
.mobileNavLink.active {
  color: #00d4ff;
  background: rgba(0, 212, 255, 0.1);
  transform: translateX(10px);
}

.mobileCta {
  background: linear-gradient(45deg, #00d4ff, #7b2cbf);
  color: white;
  text-decoration: none;
  padding: 1rem;
  border-radius: 50px;
  font-family: 'Orbitron', sans-serif;
  font-weight: 600;
  text-align: center;
  margin-top: 2rem;
  transition: all 0.3s ease;
}

.mobileCta:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(123, 44, 191, 0.4);
}

/* Mobile Overlay */
.mobileOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
}

/* Responsive Design */
@media (max-width: 768px) {
  .desktopNav,
  .ctaContainer {
    display: none;
  }

  .mobileMenuButton {
    display: flex;
  }

  .container {
    padding: 0 1rem;
  }

  .logo {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .mobileNav {
    width: 100%;
    right: -100%;
  }

  .mobileNav.open {
    right: 0;
  }
}


