.hero {
  background: linear-gradient(135deg, #f0e6f6, #ffffff);
  padding: 4rem 2rem;
  text-align: center;
  font-family: 'Arial', sans-serif;
  color: #000;
}

.logoContainer {
  margin-bottom: 2rem;
}

.logo {
  max-width: 150px;
  height: auto;
}

.title {
  font-size: 3rem;
  font-weight: 900;
  margin-bottom: 3rem;
  letter-spacing: 0.1em;
}

.eventsGrid {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.eventCard {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  width: 220px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.eventImage {
  width: 100%;
  border-radius: 12px;
  margin-bottom: 1rem;
  object-fit: cover;
  height: 140px;
}

h3 {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.reserveButton {
  background: transparent;
  border: 2px solid #000;
  padding: 0.5rem 1rem;
  font-weight: 700;
  cursor: pointer;
  border-radius: 6px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.reserveButton:hover {
  background-color: #000;
  color: #fff;
}
