/* Enhanced DigiClick AI Custom Cursor Styles with GSAP Integration and Customization Support */

/* CSS Custom Properties for Dynamic Cursor Customization */
.cursorRoot {
  --cursor-size-multiplier: 1;
  --cursor-opacity: 1;
  --cursor-color: #00d4ff;
  --cursor-border-radius: 50%;
  --cursor-particles-enabled: 1;
  --cursor-ripples-enabled: 1;
  --cursor-glow-enabled: 1;
  --cursor-hover-enabled: 1;
  --cursor-motion-override: 0;
}

.cursor {
  position: fixed;
  top: 0;
  left: 0;
  width: calc(24px * var(--cursor-size-multiplier));
  height: calc(24px * var(--cursor-size-multiplier));
  pointer-events: none;
  z-index: 10000;
  mix-blend-mode: difference;
  will-change: transform, scale, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
  opacity: var(--cursor-opacity);
  border-radius: var(--cursor-border-radius);
}

.cursorInner {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cursorCore {
  width: calc(8px * var(--cursor-size-multiplier));
  height: calc(8px * var(--cursor-size-multiplier));
  background: var(--cursor-color);
  border-radius: var(--cursor-border-radius);
  position: relative;
  z-index: 2;
  box-shadow:
    0 0 calc(10px * var(--cursor-size-multiplier) * var(--cursor-glow-enabled)) var(--cursor-color),
    0 0 calc(20px * var(--cursor-size-multiplier) * var(--cursor-glow-enabled)) var(--cursor-color),
    0 0 calc(30px * var(--cursor-size-multiplier) * var(--cursor-glow-enabled)) var(--cursor-color);
  animation: cursorPulse 2s ease-in-out infinite;
  animation-play-state: running;
}

.cursorGlow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 24px;
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(0, 212, 255, 0.1) 0%,
    rgba(0, 212, 255, 0.05) 50%,
    transparent 100%
  );
  transition: all 0.3s ease;
}

/* Hover States */
.cursor.hover {
  transform: scale(1.5);
}

.cursor.hover .cursorCore {
  background: #7b2cbf;
  box-shadow: 
    0 0 15px #7b2cbf,
    0 0 30px #7b2cbf,
    0 0 45px #7b2cbf;
}

.cursor.hover .cursorGlow {
  border-color: rgba(123, 44, 191, 0.5);
  background: radial-gradient(
    circle,
    rgba(123, 44, 191, 0.2) 0%,
    rgba(123, 44, 191, 0.1) 50%,
    transparent 100%
  );
  transform: translate(-50%, -50%) scale(1.2);
}

/* Click State */
.cursor.clicked {
  transform: scale(0.8);
}

.cursor.clicked .cursorCore {
  background: #ffffff;
  box-shadow: 
    0 0 20px #ffffff,
    0 0 40px #00d4ff,
    0 0 60px #7b2cbf;
  animation: clickPulse 0.3s ease-out;
}

/* Cursor Types */
.cursor.pointer .cursorCore {
  background: #00ff88;
  box-shadow: 
    0 0 15px #00ff88,
    0 0 30px #00ff88;
}

.cursor.pointer .cursorGlow {
  border-color: rgba(0, 255, 136, 0.5);
  background: radial-gradient(
    circle,
    rgba(0, 255, 136, 0.2) 0%,
    rgba(0, 255, 136, 0.1) 50%,
    transparent 100%
  );
}

.cursor.glow {
  transform: scale(2);
}

.cursor.glow .cursorCore {
  background: linear-gradient(45deg, #00d4ff, #7b2cbf);
  box-shadow: 
    0 0 20px #00d4ff,
    0 0 40px #7b2cbf,
    0 0 60px rgba(0, 212, 255, 0.5);
  animation: glowPulse 1s ease-in-out infinite;
}

.cursor.glow .cursorGlow {
  border: 2px solid rgba(123, 44, 191, 0.8);
  background: radial-gradient(
    circle,
    rgba(123, 44, 191, 0.3) 0%,
    rgba(0, 212, 255, 0.2) 50%,
    transparent 100%
  );
  transform: translate(-50%, -50%) scale(1.5);
}

.cursor.text .cursorCore {
  background: #ff6b6b;
  box-shadow: 
    0 0 15px #ff6b6b,
    0 0 30px #ff6b6b;
}

.cursor.text .cursorGlow {
  border-color: rgba(255, 107, 107, 0.5);
  background: radial-gradient(
    circle,
    rgba(255, 107, 107, 0.2) 0%,
    rgba(255, 107, 107, 0.1) 50%,
    transparent 100%
  );
}

/* Cursor Icons */
.cursorIcon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 8px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 5px currentColor;
  z-index: 3;
  pointer-events: none;
}

/* Enhanced Cursor Text */
.cursorText {
  position: fixed;
  pointer-events: none;
  z-index: 10001;
  font-family: 'Orbitron', monospace;
  font-size: 10px;
  font-weight: 700;
  color: #00d4ff;
  text-transform: uppercase;
  letter-spacing: 2px;
  background: rgba(0, 0, 0, 0.9);
  padding: 6px 12px;
  border-radius: 6px;
  border: 1px solid #00d4ff;
  box-shadow:
    0 0 15px rgba(0, 212, 255, 0.6),
    inset 0 0 10px rgba(0, 212, 255, 0.1);
  animation: textGlow 1s ease-in-out infinite alternate;
  backdrop-filter: blur(15px);
  transition: all 0.3s ease;
}

/* Context-specific text styles */
.ctaText {
  background: linear-gradient(45deg, rgba(0, 212, 255, 0.2), rgba(123, 44, 191, 0.2));
  border-color: #7b2cbf;
  color: #7b2cbf;
  font-weight: 800;
  animation: ctaTextGlow 1.2s ease-in-out infinite alternate;
}

.loadingText {
  background: rgba(0, 212, 255, 0.1);
  border-color: #00d4ff;
  animation: loadingTextPulse 1s ease-in-out infinite;
}

.disabledText {
  background: rgba(255, 68, 68, 0.1);
  border-color: #ff4444;
  color: #ff4444;
  opacity: 0.7;
}

.successText {
  background: rgba(0, 255, 136, 0.1);
  border-color: #00ff88;
  color: #00ff88;
  animation: successTextGlow 1s ease-in-out infinite alternate;
}

.errorText {
  background: rgba(255, 68, 68, 0.1);
  border-color: #ff4444;
  color: #ff4444;
  animation: errorTextShake 0.5s ease-in-out infinite;
}

/* Animations */
@keyframes cursorPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes clickPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.5);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes glowPulse {
  0%, 100% {
    box-shadow: 
      0 0 20px #00d4ff,
      0 0 40px #7b2cbf,
      0 0 60px rgba(0, 212, 255, 0.5);
  }
  50% {
    box-shadow: 
      0 0 30px #00d4ff,
      0 0 60px #7b2cbf,
      0 0 90px rgba(0, 212, 255, 0.8);
  }
}

@keyframes textGlow {
  0% {
    text-shadow: 0 0 5px #00d4ff;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
  }
  100% {
    text-shadow: 0 0 10px #00d4ff, 0 0 20px #00d4ff;
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.8);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .cursor {
    display: none;
  }
  
  .cursorText {
    display: none;
  }
}

/* Touch Device Detection */
@media (hover: none) and (pointer: coarse) {
  .cursor {
    display: none;
  }
  
  .cursorText {
    display: none;
  }
}

/* High Performance Mode */
@media (prefers-reduced-motion: reduce) {
  .cursor {
    animation: none;
    transition: none;
  }
  
  .cursorCore {
    animation: none;
  }
  
  .cursorGlow {
    transition: none;
  }
  
  .cursorText {
    animation: none;
  }
}

/* Dark Mode Adjustments */
@media (prefers-color-scheme: dark) {
  .cursor {
    mix-blend-mode: screen;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .cursorCore {
    background: #ffffff;
    box-shadow: 
      0 0 5px #ffffff,
      0 0 10px #ffffff;
  }
  
  .cursorGlow {
    border-color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
  }
  
  .cursorText {
    color: #ffffff;
    border-color: #ffffff;
    background: #000000;
  }
}

/* Enhanced Context-Aware Cursor States */

/* CTA Button Hover State - Larger glowing circle (20px → 30px) */
.cursor.cta {
  width: 30px;
  height: 30px;
}

.cursor.cta .cursorCore {
  width: 12px;
  height: 12px;
  background: linear-gradient(45deg, #00d4ff, #7b2cbf, #ff6b6b);
  box-shadow:
    0 0 25px #00d4ff,
    0 0 50px #7b2cbf,
    0 0 75px rgba(0, 212, 255, 0.8);
  animation: ctaPulse 1.2s ease-in-out infinite;
}

.cursor.cta .cursorGlow {
  width: 30px;
  height: 30px;
  border: 2px solid rgba(0, 212, 255, 0.9);
  background: radial-gradient(
    circle,
    rgba(0, 212, 255, 0.4) 0%,
    rgba(123, 44, 191, 0.3) 50%,
    rgba(255, 107, 107, 0.2) 100%
  );
}

/* Navigation Link Hover State - Arrow pointer with trailing particle effect */
.cursor.nav .cursorCore {
  background: #7b2cbf;
  box-shadow:
    0 0 15px #7b2cbf,
    0 0 30px rgba(123, 44, 191, 0.6);
  animation: navGlow 2s ease-in-out infinite;
  position: relative;
}

.cursor.nav .cursorCore::after {
  content: '→';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #7b2cbf;
  font-size: 8px;
  font-weight: bold;
}

/* Text Input Cursor State - I-beam with typing indicator */
.cursor.text {
  width: 8px;
  height: 32px;
}

.cursor.text .cursorCore {
  width: 2px;
  height: 20px;
  background: #00d4ff;
  border-radius: 1px;
  box-shadow:
    0 0 10px #00d4ff,
    0 0 20px rgba(0, 212, 255, 0.5);
  animation: textBlink 1s ease-in-out infinite;
}

.cursor.text .cursorGlow {
  width: 8px;
  height: 32px;
  border-radius: 4px;
  background: linear-gradient(
    to bottom,
    rgba(0, 212, 255, 0.2) 0%,
    rgba(0, 212, 255, 0.4) 50%,
    rgba(0, 212, 255, 0.2) 100%
  );
}

/* Zoom Cursor State - Expand with zoom icon and pulsing animation */
.cursor.zoom .cursorCore {
  background: radial-gradient(circle, #00d4ff, #7b2cbf);
  box-shadow:
    0 0 20px #00d4ff,
    0 0 40px #7b2cbf,
    0 0 60px rgba(0, 212, 255, 0.6);
  animation: zoomPulse 1.5s ease-in-out infinite;
}

.cursor.zoom .cursorGlow {
  animation: zoomGlow 1.5s ease-in-out infinite;
}

/* Drag Cursor State - Custom drag cursor with directional arrows */
.cursor.drag .cursorCore {
  background: conic-gradient(from 0deg, #00d4ff, #7b2cbf, #ff6b6b, #00d4ff);
  box-shadow:
    0 0 25px #00d4ff,
    0 0 50px rgba(123, 44, 191, 0.7);
  animation: dragRotate 2s linear infinite;
}

.cursor.dragging .cursorCore {
  background: linear-gradient(45deg, #ff6b6b, #00d4ff);
  box-shadow:
    0 0 30px #ff6b6b,
    0 0 60px #00d4ff;
  animation: draggingPulse 0.5s ease-in-out infinite;
}

/* Upload Cursor State - Upload icon with animated cloud/arrow */
.cursor.upload .cursorCore {
  background: linear-gradient(135deg, #00d4ff, #7b2cbf, #00ff88);
  box-shadow:
    0 0 20px #00d4ff,
    0 0 40px rgba(0, 255, 136, 0.6);
  animation: uploadFloat 2s ease-in-out infinite;
}

/* Loading Cursor State - Spinning animation */
.cursor.loading .cursorCore {
  background: conic-gradient(from 0deg, #00d4ff 0%, transparent 50%, #7b2cbf 100%);
  box-shadow:
    0 0 20px #00d4ff,
    0 0 40px #7b2cbf;
  animation: loadingSpin 1s linear infinite;
}

/* Disabled Cursor State - Reduced opacity with red accent */
.cursor.disabled {
  opacity: 0.5;
}

.cursor.disabled .cursorCore {
  background: linear-gradient(45deg, #ff4444, #666666);
  box-shadow:
    0 0 15px #ff4444,
    0 0 30px rgba(255, 68, 68, 0.5);
  animation: disabledPulse 2s ease-in-out infinite;
}

.cursor.disabled .cursorGlow {
  border-color: rgba(255, 68, 68, 0.6);
  background: radial-gradient(
    circle,
    rgba(255, 68, 68, 0.2) 0%,
    rgba(102, 102, 102, 0.1) 50%,
    transparent 100%
  );
}

/* Form Validation States */
.cursor.success .cursorCore {
  background: linear-gradient(45deg, #00ff88, #00d4ff);
  box-shadow:
    0 0 20px #00ff88,
    0 0 40px rgba(0, 255, 136, 0.7);
  animation: successPulse 1s ease-in-out infinite;
}

.cursor.error .cursorCore {
  background: linear-gradient(45deg, #ff4444, #ff6b6b);
  box-shadow:
    0 0 20px #ff4444,
    0 0 40px rgba(255, 68, 68, 0.7);
  animation: errorShake 0.5s ease-in-out infinite;
}

/* Glow Text Hover State */
.cursor.glow .cursorCore {
  background: linear-gradient(45deg, #00d4ff, #7b2cbf);
  box-shadow:
    0 0 30px #00d4ff,
    0 0 60px #7b2cbf,
    0 0 90px rgba(0, 212, 255, 0.7);
  animation: glowIntense 1s ease-in-out infinite;
}

/* Pulse Box Hover State */
.cursor.pulse .cursorCore {
  background: radial-gradient(circle, #ff6b6b, #00d4ff, #7b2cbf);
  animation: pulseBeat 0.8s ease-in-out infinite;
}

/* Glow Trigger Hover State */
.cursor.trigger .cursorCore {
  background: conic-gradient(from 0deg, #00d4ff, #7b2cbf, #ff6b6b, #00d4ff);
  box-shadow:
    0 0 40px #00d4ff,
    0 0 80px #7b2cbf,
    0 0 120px rgba(255, 107, 107, 0.5);
  animation: triggerSpin 2s linear infinite;
}

/* Trail Particles */
.trailParticle {
  position: fixed;
  border-radius: 50%;
  background: radial-gradient(circle, #00d4ff 0%, transparent 70%);
  pointer-events: none;
  z-index: 9999;
  box-shadow: 0 0 10px #00d4ff;
  will-change: transform, opacity, scale;
}

/* Click Ripple Effect */
.clickRipple {
  position: fixed;
  border: 2px solid #00d4ff;
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  box-shadow:
    0 0 20px #00d4ff,
    inset 0 0 20px rgba(0, 212, 255, 0.3);
  will-change: transform, opacity, scale;
}

/* Containers */
.trailContainer,
.rippleContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9998;
}

/* Enhanced Animations */
@keyframes ctaPulse {
  0%, 100% {
    transform: scale(1);
    filter: hue-rotate(0deg);
  }
  50% {
    transform: scale(1.1);
    filter: hue-rotate(90deg);
  }
}

@keyframes navGlow {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

@keyframes glowIntense {
  0%, 100% {
    box-shadow:
      0 0 30px #00d4ff,
      0 0 60px #7b2cbf,
      0 0 90px rgba(0, 212, 255, 0.7);
  }
  50% {
    box-shadow:
      0 0 40px #00d4ff,
      0 0 80px #7b2cbf,
      0 0 120px rgba(0, 212, 255, 0.9);
  }
}

@keyframes pulseBeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

@keyframes triggerSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* New Context-Aware Animations */
@keyframes textBlink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

@keyframes zoomPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow:
      0 0 20px #00d4ff,
      0 0 40px #7b2cbf,
      0 0 60px rgba(0, 212, 255, 0.6);
  }
  50% {
    transform: scale(1.1);
    box-shadow:
      0 0 30px #00d4ff,
      0 0 60px #7b2cbf,
      0 0 90px rgba(0, 212, 255, 0.8);
  }
}

@keyframes zoomGlow {
  0%, 100% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(2.5);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(3);
  }
}

@keyframes dragRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes draggingPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

@keyframes uploadFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

@keyframes loadingSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes disabledPulse {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.3;
  }
}

@keyframes successPulse {
  0%, 100% {
    box-shadow:
      0 0 20px #00ff88,
      0 0 40px rgba(0, 255, 136, 0.7);
  }
  50% {
    box-shadow:
      0 0 30px #00ff88,
      0 0 60px rgba(0, 255, 136, 0.9);
  }
}

@keyframes errorShake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-2px);
  }
  75% {
    transform: translateX(2px);
  }
}

/* Enhanced Text Animations */
@keyframes ctaTextGlow {
  0% {
    text-shadow: 0 0 5px #7b2cbf;
    box-shadow:
      0 0 15px rgba(123, 44, 191, 0.6),
      inset 0 0 10px rgba(123, 44, 191, 0.1);
  }
  100% {
    text-shadow: 0 0 15px #7b2cbf, 0 0 25px #7b2cbf;
    box-shadow:
      0 0 25px rgba(123, 44, 191, 0.8),
      inset 0 0 15px rgba(123, 44, 191, 0.2);
  }
}

@keyframes loadingTextPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes successTextGlow {
  0% {
    text-shadow: 0 0 5px #00ff88;
    box-shadow:
      0 0 15px rgba(0, 255, 136, 0.6),
      inset 0 0 10px rgba(0, 255, 136, 0.1);
  }
  100% {
    text-shadow: 0 0 15px #00ff88, 0 0 25px #00ff88;
    box-shadow:
      0 0 25px rgba(0, 255, 136, 0.8),
      inset 0 0 15px rgba(0, 255, 136, 0.2);
  }
}

@keyframes errorTextShake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-1px);
  }
  75% {
    transform: translateX(1px);
  }
}

/* Performance Optimizations */
.cursor,
.cursorInner,
.cursorCore,
.cursorGlow,
.cursorText,
.trailParticle,
.clickRipple {
  will-change: transform, opacity, scale;
  backface-visibility: hidden;
  perspective: 1000px;
  transform: translate3d(0, 0, 0);
}

/* Theme Variations */
.cursor.minimal .cursorCore {
  background: #ffffff;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.cursor.neon .cursorCore {
  background: #00ff00;
  box-shadow:
    0 0 20px #00ff00,
    0 0 40px #00ff00,
    0 0 60px #00ff00;
}

.cursor.corporate .cursorCore {
  background: #333333;
  box-shadow: 0 0 15px rgba(51, 51, 51, 0.8);
}
