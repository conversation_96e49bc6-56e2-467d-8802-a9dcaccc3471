.pricing {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'Arial', sans-serif;
  text-align: center;
}

.title {
  font-size: 3rem;
  font-weight: 900;
  margin-bottom: 2rem;
}

.billingToggle {
  margin-bottom: 2rem;
}

.billingToggle button {
  background: none;
  border: 2px solid #7b2cbf;
  color: #7b2cbf;
  padding: 0.5rem 1.5rem;
  margin: 0 0.5rem;
  font-weight: 700;
  cursor: pointer;
  border-radius: 30px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.billingToggle button.active,
.billingToggle button:hover {
  background-color: #7b2cbf;
  color: #fff;
}

.plansGrid {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.planCard {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  width: 280px;
  padding: 2rem 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  transition: transform 0.3s ease;
}

.planCard:hover {
  transform: translateY(-10px);
}

.popular {
  border: 3px solid #7b2cbf;
}

.popularBadge {
  position: absolute;
  top: -12px;
  right: -12px;
  background-color: #7b2cbf;
  color: #fff;
  padding: 0.25rem 0.75rem;
  font-weight: 700;
  border-radius: 20px;
  font-size: 0.75rem;
}

.planTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.planPrice {
  font-size: 2rem;
  font-weight: 900;
  margin-bottom: 1.5rem;
  color: #7b2cbf;
}

.planFeatures {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
  text-align: left;
  width: 100%;
}

.planFeatures li {
  margin-bottom: 0.75rem;
  padding-left: 1.25rem;
  position: relative;
}

.planFeatures li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #7b2cbf;
  font-weight: 700;
}

.ctaButton {
  background-color: #7b2cbf;
  color: #fff;
  border: none;
  padding: 0.75rem 2rem;
  font-weight: 700;
  border-radius: 30px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.ctaButton:hover {
  background-color: #5a1a9e;
}
