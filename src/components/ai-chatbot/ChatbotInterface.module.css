/**
 * <PERSON><PERSON><PERSON>lick AI Chatbot Interface Styles
 * Futuristic theme with accessibility compliance
 * Maintains 60fps performance and WCAG 2.1 AA standards
 */

/* Toggle Button */
.toggleButton {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #00d4ff 0%, #7b2cbf 100%);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 32px rgba(0, 212, 255, 0.3);
  transition: all 0.3s ease;
  z-index: 1000;
  color: white;
}

.toggleButton:hover {
  transform: scale(1.1);
  box-shadow: 0 12px 40px rgba(0, 212, 255, 0.4);
}

.toggleButton:focus {
  outline: 2px solid #00d4ff;
  outline-offset: 2px;
}

.toggleIcon {
  width: 24px;
  height: 24px;
  transition: transform 0.3s ease;
}

.toggleButton:hover .toggleIcon {
  transform: rotate(180deg);
}

.loadingIndicator {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  border: 2px solid transparent;
  border-top: 2px solid #00d4ff;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Chat Container */
.chatContainer {
  position: fixed;
  bottom: 6rem;
  right: 2rem;
  width: 380px;
  height: 600px;
  background: #121212;
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  z-index: 999;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

/* Header */
.header {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(123, 44, 191, 0.1) 100%);
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.headerContent {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #00d4ff 0%, #7b2cbf 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.avatar svg {
  width: 20px;
  height: 20px;
}

.headerText h3 {
  margin: 0;
  font-family: 'Orbitron', monospace;
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
}

.status {
  margin: 0;
  font-family: 'Poppins', sans-serif;
  font-size: 0.75rem;
  color: #00d4ff;
  opacity: 0.8;
}

.headerActions {
  display: flex;
  gap: 0.5rem;
}

.actionButton {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.actionButton:hover {
  background: rgba(0, 212, 255, 0.2);
  transform: scale(1.05);
}

.actionButton:focus {
  outline: 2px solid #00d4ff;
  outline-offset: 1px;
}

.actionButton svg {
  width: 16px;
  height: 16px;
}

/* Messages Container */
.messagesContainer {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  scroll-behavior: smooth;
}

.messages::-webkit-scrollbar {
  width: 6px;
}

.messages::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.messages::-webkit-scrollbar-thumb {
  background: rgba(0, 212, 255, 0.3);
  border-radius: 3px;
}

.messages::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 212, 255, 0.5);
}

/* Welcome Message */
.welcomeMessage {
  text-align: center;
  padding: 2rem 1rem;
  color: rgba(255, 255, 255, 0.7);
  font-family: 'Poppins', sans-serif;
}

.welcomeMessage p {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Messages */
.message {
  display: flex;
  flex-direction: column;
  max-width: 80%;
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message.user {
  align-self: flex-end;
  align-items: flex-end;
}

.message.assistant {
  align-self: flex-start;
  align-items: flex-start;
}

.messageContent {
  padding: 0.75rem 1rem;
  border-radius: 16px;
  font-family: 'Poppins', sans-serif;
  font-size: 0.9rem;
  line-height: 1.4;
  word-wrap: break-word;
}

.message.user .messageContent {
  background: linear-gradient(135deg, #00d4ff 0%, #7b2cbf 100%);
  color: white;
  border-bottom-right-radius: 4px;
}

.message.assistant .messageContent {
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-bottom-left-radius: 4px;
}

.messageContent p {
  margin: 0;
}

.messageTime {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 0.25rem;
  font-family: 'Poppins', sans-serif;
}

/* Typing Indicator */
.typingIndicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 0.75rem 1rem;
}

.typingIndicator span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #00d4ff;
  animation: typingDot 1.4s infinite ease-in-out;
}

.typingIndicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typingIndicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typingDot {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Input Container */
.inputContainer {
  border-top: 1px solid rgba(0, 212, 255, 0.2);
  padding: 1rem;
  background: rgba(0, 0, 0, 0.3);
}

.inputWrapper {
  display: flex;
  align-items: flex-end;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 12px;
  padding: 0.5rem;
  transition: border-color 0.2s ease;
}

.inputWrapper:focus-within {
  border-color: #00d4ff;
  box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
}

.input {
  flex: 1;
  background: transparent;
  border: none;
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
  font-size: 0.9rem;
  resize: none;
  outline: none;
  min-height: 20px;
  max-height: 100px;
  line-height: 1.4;
}

.input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.voiceButton {
  width: 36px;
  height: 36px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.voiceButton:hover {
  background: rgba(0, 212, 255, 0.2);
  transform: scale(1.05);
}

.voiceButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.voiceButton.listening {
  background: #00d4ff;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 212, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 212, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 212, 255, 0);
  }
}

.voiceButton svg {
  width: 18px;
  height: 18px;
}

.sendButton {
  width: 36px;
  height: 36px;
  border: none;
  background: linear-gradient(135deg, #00d4ff 0%, #7b2cbf 100%);
  border-radius: 8px;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.sendButton:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
}

.sendButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.sendButton svg {
  width: 18px;
  height: 18px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .chatContainer {
    bottom: 0;
    right: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    border-radius: 0;
    border: none;
  }
  
  .toggleButton {
    bottom: 1rem;
    right: 1rem;
  }
}

@media (max-width: 480px) {
  .toggleButton {
    width: 50px;
    height: 50px;
  }
  
  .toggleIcon {
    width: 20px;
    height: 20px;
  }
}

/* High Contrast Mode */
.highContrast {
  background: #000000 !important;
  border-color: #ffffff !important;
}

.highContrast .header {
  background: #000000 !important;
  border-bottom-color: #ffffff !important;
}

.highContrast .headerText h3,
.highContrast .status,
.highContrast .messageContent,
.highContrast .input {
  color: #ffffff !important;
}

.highContrast .message.assistant .messageContent {
  background: #333333 !important;
  border-color: #ffffff !important;
}

.highContrast .inputWrapper {
  background: #333333 !important;
  border-color: #ffffff !important;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .toggleButton,
  .actionButton,
  .voiceButton,
  .sendButton {
    transition: none;
  }
  
  .toggleButton:hover .toggleIcon {
    transform: none;
  }
  
  .message {
    animation: none;
  }
  
  .typingIndicator span {
    animation: none;
  }
  
  .voiceButton.listening {
    animation: none;
  }
  
  .loadingIndicator {
    animation: none;
  }
}

/* Focus Management */
.chatContainer:focus-within {
  outline: 2px solid #00d4ff;
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .toggleButton,
  .chatContainer {
    display: none;
  }
}
