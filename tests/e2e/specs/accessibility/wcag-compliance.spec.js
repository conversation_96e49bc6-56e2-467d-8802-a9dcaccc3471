/**
 * <PERSON><PERSON><PERSON>lick AI WCAG 2.1 AA Compliance E2E Tests
 * Comprehensive accessibility testing across all pages and features
 */

import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

test.describe('WCAG 2.1 AA Compliance Tests', () => {
  const pages = [
    { path: '/', name: 'Home' },
    { path: '/about', name: 'About' },
    { path: '/services', name: 'Services' },
    { path: '/pricing', name: 'Pricing' },
    { path: '/contact', name: 'Contact' },
    { path: '/blog', name: 'Blog' },
    { path: '/faq', name: 'FAQ' },
    { path: '/privacy', name: 'Privacy' },
    { path: '/terms', name: 'Terms' }
  ];

  for (const pageInfo of pages) {
    test(`should meet WCAG 2.1 AA standards on ${pageInfo.name} page`, async ({ page }) => {
      await page.goto(pageInfo.path);
      await page.waitForLoadState('networkidle');

      // Run axe accessibility scan
      const accessibilityScanResults = await new AxeBuilder({ page })
        .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
        .analyze();

      expect(accessibilityScanResults.violations).toEqual([]);
    });
  }

  test('should support keyboard navigation across all interactive elements', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Test keyboard navigation
    const interactiveElements = await page.locator('button, a, input, textarea, select, [tabindex]:not([tabindex="-1"])').all();
    
    let currentIndex = 0;
    for (const element of interactiveElements.slice(0, 20)) { // Test first 20 elements
      await page.keyboard.press('Tab');
      
      // Check if element is focused and visible
      const isFocused = await element.evaluate(el => document.activeElement === el);
      const isVisible = await element.isVisible();
      
      if (isVisible) {
        expect(isFocused).toBeTruthy();
        
        // Test focus indicators
        const focusStyles = await element.evaluate(el => {
          const styles = window.getComputedStyle(el);
          return {
            outline: styles.outline,
            outlineWidth: styles.outlineWidth,
            boxShadow: styles.boxShadow
          };
        });
        
        // Should have visible focus indicator
        const hasFocusIndicator = focusStyles.outline !== 'none' || 
                                 focusStyles.outlineWidth !== '0px' || 
                                 focusStyles.boxShadow !== 'none';
        expect(hasFocusIndicator).toBeTruthy();
      }
      
      currentIndex++;
    }
  });

  test('should support screen reader navigation with proper ARIA labels', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Test main landmarks
    await expect(page.locator('main')).toHaveAttribute('role', 'main');
    await expect(page.locator('nav')).toHaveAttribute('role', 'navigation');
    await expect(page.locator('header')).toHaveAttribute('role', 'banner');
    await expect(page.locator('footer')).toHaveAttribute('role', 'contentinfo');

    // Test heading hierarchy
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
    let previousLevel = 0;
    
    for (const heading of headings) {
      const tagName = await heading.evaluate(el => el.tagName.toLowerCase());
      const currentLevel = parseInt(tagName.charAt(1));
      
      // Heading levels should not skip (e.g., h1 -> h3)
      if (previousLevel > 0) {
        expect(currentLevel - previousLevel).toBeLessThanOrEqual(1);
      }
      
      previousLevel = currentLevel;
    }

    // Test ARIA labels on interactive elements
    const buttons = await page.locator('button').all();
    for (const button of buttons) {
      const hasLabel = await button.evaluate(el => {
        return el.getAttribute('aria-label') || 
               el.getAttribute('aria-labelledby') || 
               el.textContent.trim().length > 0;
      });
      expect(hasLabel).toBeTruthy();
    }
  });

  test('should meet color contrast requirements', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Test color contrast using axe
    const contrastResults = await new AxeBuilder({ page })
      .withTags(['color-contrast'])
      .analyze();

    expect(contrastResults.violations).toEqual([]);

    // Test specific DigiClick AI color combinations
    const colorTests = [
      { selector: '[data-testid="primary-text"]', minContrast: 4.5 },
      { selector: '[data-testid="secondary-text"]', minContrast: 4.5 },
      { selector: '[data-testid="accent-text"]', minContrast: 4.5 },
      { selector: 'button', minContrast: 3.0 }, // Large text
      { selector: 'a', minContrast: 4.5 }
    ];

    for (const colorTest of colorTests) {
      const elements = await page.locator(colorTest.selector).all();
      
      for (const element of elements.slice(0, 5)) { // Test first 5 of each type
        const isVisible = await element.isVisible();
        if (!isVisible) continue;

        const contrastRatio = await element.evaluate((el, minContrast) => {
          const styles = window.getComputedStyle(el);
          const color = styles.color;
          const backgroundColor = styles.backgroundColor;
          
          // Simple contrast calculation (in real implementation, use proper contrast calculation)
          return { color, backgroundColor, passes: true }; // Simplified for demo
        }, colorTest.minContrast);

        expect(contrastRatio.passes).toBeTruthy();
      }
    }
  });

  test('should support reduced motion preferences', async ({ page }) => {
    // Test with reduced motion preference
    await page.emulateMedia({ reducedMotion: 'reduce' });
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Test that animations are disabled or reduced
    const animatedElements = await page.locator('[data-animation], .animate, .transition').all();
    
    for (const element of animatedElements.slice(0, 10)) {
      const animationState = await element.evaluate(el => {
        const styles = window.getComputedStyle(el);
        return {
          animationDuration: styles.animationDuration,
          transitionDuration: styles.transitionDuration,
          animationPlayState: styles.animationPlayState
        };
      });

      // Animations should be disabled or very short
      const isReduced = animationState.animationDuration === '0s' || 
                       animationState.transitionDuration === '0s' ||
                       animationState.animationPlayState === 'paused';
      
      expect(isReduced).toBeTruthy();
    }
  });

  test('should provide proper form accessibility', async ({ page }) => {
    await page.goto('/contact');
    await page.waitForLoadState('networkidle');

    // Test form labels
    const formInputs = await page.locator('input, textarea, select').all();
    
    for (const input of formInputs) {
      const hasLabel = await input.evaluate(el => {
        const id = el.id;
        const ariaLabel = el.getAttribute('aria-label');
        const ariaLabelledBy = el.getAttribute('aria-labelledby');
        const label = id ? document.querySelector(`label[for="${id}"]`) : null;
        
        return !!(ariaLabel || ariaLabelledBy || label);
      });
      
      expect(hasLabel).toBeTruthy();
    }

    // Test required field indicators
    const requiredInputs = await page.locator('input[required], textarea[required]').all();
    
    for (const input of requiredInputs) {
      const hasRequiredIndicator = await input.evaluate(el => {
        return el.getAttribute('aria-required') === 'true' ||
               el.getAttribute('required') !== null;
      });
      
      expect(hasRequiredIndicator).toBeTruthy();
    }

    // Test error message accessibility
    const nameInput = page.locator('[data-testid="contact-name"]');
    await nameInput.focus();
    await nameInput.blur();

    // Check for error message with proper ARIA
    const errorMessage = page.locator('[data-testid="name-error"]');
    if (await errorMessage.isVisible()) {
      await expect(errorMessage).toHaveAttribute('role', 'alert');
      await expect(errorMessage).toHaveAttribute('aria-live', 'polite');
    }
  });

  test('should support cursor customization accessibility', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Open cursor customization panel
    await page.locator('[data-testid="cursor-settings-trigger"]').click();
    await expect(page.locator('[data-testid="cursor-customization-panel"]')).toBeVisible();

    // Test panel accessibility
    await expect(page.locator('[data-testid="cursor-customization-panel"]')).toHaveAttribute('role', 'dialog');
    await expect(page.locator('[data-testid="cursor-customization-panel"]')).toHaveAttribute('aria-modal', 'true');

    // Test keyboard navigation within panel
    await page.keyboard.press('Tab');
    const firstFocusable = await page.evaluate(() => document.activeElement.getAttribute('data-testid'));
    expect(firstFocusable).toBeTruthy();

    // Test escape key to close
    await page.keyboard.press('Escape');
    await expect(page.locator('[data-testid="cursor-customization-panel"]')).not.toBeVisible();

    // Test reduced motion impact on cursor
    await page.emulateMedia({ reducedMotion: 'reduce' });
    await page.reload();
    await page.waitForLoadState('networkidle');

    const cursorEffects = await page.evaluate(() => {
      const cursor = window.getCustomCursor?.();
      return cursor ? cursor.getSettings() : null;
    });

    if (cursorEffects) {
      expect(cursorEffects.respectsReducedMotion).toBeTruthy();
    }
  });

  test('should provide accessible navigation enhancements', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Test breadcrumb navigation
    await page.goto('/services');
    await page.waitForLoadState('networkidle');

    const breadcrumbs = page.locator('[data-testid="breadcrumb-navigation"]');
    if (await breadcrumbs.isVisible()) {
      await expect(breadcrumbs).toHaveAttribute('role', 'navigation');
      await expect(breadcrumbs).toHaveAttribute('aria-label', /breadcrumb/i);

      // Test breadcrumb links
      const breadcrumbLinks = await breadcrumbs.locator('a').all();
      for (const link of breadcrumbLinks) {
        await expect(link).toHaveAttribute('href');
      }
    }

    // Test skip links
    await page.keyboard.press('Tab');
    const skipLink = page.locator('[data-testid="skip-to-main"]');
    if (await skipLink.isVisible()) {
      await expect(skipLink).toHaveAttribute('href', '#main');
      await skipLink.click();
      
      const mainContent = page.locator('#main');
      const isFocused = await mainContent.evaluate(el => document.activeElement === el);
      expect(isFocused).toBeTruthy();
    }
  });

  test('should maintain accessibility during page transitions', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Test page transition accessibility
    await page.locator('a[href="/about"]').first().click();
    
    // Test loading state accessibility
    const loadingIndicator = page.locator('[data-testid="page-loading"]');
    if (await loadingIndicator.isVisible()) {
      await expect(loadingIndicator).toHaveAttribute('role', 'status');
      await expect(loadingIndicator).toHaveAttribute('aria-live', 'polite');
    }

    await page.waitForLoadState('networkidle');

    // Test focus management after transition
    const pageTitle = page.locator('h1').first();
    const isFocused = await pageTitle.evaluate(el => document.activeElement === el);
    expect(isFocused).toBeTruthy();

    // Run accessibility scan on new page
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('should support high contrast mode', async ({ page }) => {
    // Simulate high contrast mode
    await page.addStyleTag({
      content: `
        @media (prefers-contrast: high) {
          * {
            background-color: black !important;
            color: white !important;
            border-color: white !important;
          }
        }
      `
    });

    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Test that high contrast styles are applied
    const bodyStyles = await page.locator('body').evaluate(el => {
      return window.getComputedStyle(el);
    });

    // Verify accessibility manager handles high contrast
    const highContrastSupport = await page.evaluate(() => {
      const accessibilityManager = window.getAccessibilityManager?.();
      return accessibilityManager ? accessibilityManager.supportsHighContrast() : false;
    });

    expect(highContrastSupport).toBeTruthy();
  });
});
