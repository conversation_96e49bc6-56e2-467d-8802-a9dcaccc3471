name: DigiClick AI Testing & QA Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  NODE_VERSION: '18'
  NEXT_PUBLIC_API_URL: ${{ secrets.NEXT_PUBLIC_API_URL }}
  NEXT_PUBLIC_APP_URL: ${{ secrets.NEXT_PUBLIC_APP_URL }}

jobs:
  production-readiness:
    name: Production Readiness Validation
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run production readiness validation
        run: npm run production:validate

      - name: Upload validation report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: production-readiness-report
          path: |
            production-readiness-report.json
            production-readiness-report.md

  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run unit tests with coverage
        run: npm run test:coverage

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        project: [chromium, firefox, webkit]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright browsers
        run: npx playwright install --with-deps

      - name: Build application
        run: npm run build

      - name: Run E2E tests
        run: npx playwright test --project=${{ matrix.project }}

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-results-${{ matrix.project }}
          path: |
            test-results/
            playwright-report/

  accessibility-tests:
    name: Accessibility Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright browsers
        run: npx playwright install --with-deps

      - name: Build application
        run: npm run build

      - name: Run accessibility tests
        run: npm run test:accessibility

      - name: Run axe-core CLI tests
        run: npm run test:axe

      - name: Upload accessibility report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: accessibility-report
          path: |
            test-results/accessibility/
            axe-results/

  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright browsers
        run: npx playwright install --with-deps

      - name: Build application
        run: npm run build

      - name: Run performance tests
        run: npm run test:performance

      - name: Run Lighthouse CI
        run: npm run test:lighthouse

      - name: Upload performance report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: performance-report
          path: |
            test-results/performance/
            .lighthouseci/

  visual-regression:
    name: Visual Regression Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright browsers
        run: npx playwright install --with-deps

      - name: Build application
        run: npm run build

      - name: Run visual regression tests
        run: npm run test:visual

      - name: Upload visual test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: visual-regression-report
          path: |
            test-results/visual/
            test-results/screenshots/

  mobile-tests:
    name: Mobile Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright browsers
        run: npx playwright install --with-deps

      - name: Build application
        run: npm run build

      - name: Run mobile tests
        run: npm run test:mobile

      - name: Upload mobile test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: mobile-test-report
          path: |
            test-results/mobile/

  critical-journey-tests:
    name: Critical Journey Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright browsers
        run: npx playwright install --with-deps

      - name: Build application
        run: npm run build

      - name: Run critical journey tests
        run: npm run test:critical

      - name: Upload critical journey results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: critical-journey-report
          path: |
            test-results/critical-journeys/

  test-report:
    name: Generate Test Report
    runs-on: ubuntu-latest
    needs: [production-readiness, unit-tests, e2e-tests, accessibility-tests, performance-tests, visual-regression, mobile-tests, critical-journey-tests]
    if: always()
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download all artifacts
        uses: actions/download-artifact@v3

      - name: Generate comprehensive test report
        run: |
          echo "# DigiClick AI Test Report" > test-summary.md
          echo "## Test Results Summary" >> test-summary.md
          echo "- **Production Readiness**: ${{ needs.production-readiness.result }}" >> test-summary.md
          echo "- **Unit Tests**: ${{ needs.unit-tests.result }}" >> test-summary.md
          echo "- **E2E Tests**: ${{ needs.e2e-tests.result }}" >> test-summary.md
          echo "- **Accessibility Tests**: ${{ needs.accessibility-tests.result }}" >> test-summary.md
          echo "- **Performance Tests**: ${{ needs.performance-tests.result }}" >> test-summary.md
          echo "- **Visual Regression**: ${{ needs.visual-regression.result }}" >> test-summary.md
          echo "- **Mobile Tests**: ${{ needs.mobile-tests.result }}" >> test-summary.md
          echo "- **Critical Journey Tests**: ${{ needs.critical-journey-tests.result }}" >> test-summary.md
          echo "" >> test-summary.md
          echo "Generated on: $(date)" >> test-summary.md

      - name: Upload comprehensive test report
        uses: actions/upload-artifact@v3
        with:
          name: comprehensive-test-report
          path: |
            test-summary.md
            */

  notify-results:
    name: Notify Test Results
    runs-on: ubuntu-latest
    needs: [test-report]
    if: always()
    steps:
      - name: Notify Slack on success
        if: ${{ needs.test-report.result == 'success' }}
        uses: 8398a7/action-slack@v3
        with:
          status: success
          channel: '#digiclick-testing'
          text: '✅ All DigiClick AI tests passed successfully!'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify Slack on failure
        if: ${{ needs.test-report.result == 'failure' }}
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          channel: '#digiclick-testing'
          text: '❌ DigiClick AI tests failed. Please check the results.'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Create GitHub issue on critical failure
        if: ${{ needs.critical-journey-tests.result == 'failure' || needs.accessibility-tests.result == 'failure' }}
        uses: actions/github-script@v6
        with:
          script: |
            github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: '🚨 Critical Test Failure in DigiClick AI',
              body: `Critical tests have failed in the latest run. Please investigate immediately.
              
              **Failed Tests:**
              - Critical Journey Tests: ${{ needs.critical-journey-tests.result }}
              - Accessibility Tests: ${{ needs.accessibility-tests.result }}
              
              **Workflow Run:** ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}`,
              labels: ['bug', 'critical', 'testing']
            });
