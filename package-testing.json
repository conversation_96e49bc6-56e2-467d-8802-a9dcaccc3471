{"name": "digiclick-ai-testing", "version": "1.0.0", "description": "DigiClick AI Testing Infrastructure", "scripts": {"test": "npm run test:unit && npm run test:e2e", "test:unit": "jest --config jest.config.js", "test:e2e": "playwright test --config tests/e2e/playwright.config.js", "test:e2e:headed": "playwright test --config tests/e2e/playwright.config.js --headed", "test:e2e:debug": "playwright test --config tests/e2e/playwright.config.js --debug", "test:accessibility": "playwright test --config tests/e2e/playwright.config.js --project=accessibility", "test:performance": "playwright test --config tests/e2e/playwright.config.js --project=performance", "test:visual": "playwright test --config tests/e2e/playwright.config.js --project=visual", "test:mobile": "playwright test --config tests/e2e/playwright.config.js --project='Mobile Chrome' --project='Mobile Safari'", "test:critical": "playwright test --config tests/e2e/playwright.config.js tests/e2e/specs/critical-journeys/", "test:smoke": "playwright test --config tests/e2e/playwright.config.js --grep='@smoke'", "test:regression": "playwright test --config tests/e2e/playwright.config.js --grep='@regression'", "test:ci": "playwright test --config tests/e2e/playwright.config.js --reporter=github", "test:report": "playwright show-report", "test:install": "playwright install", "test:install-deps": "playwright install-deps", "test:update-snapshots": "playwright test --config tests/e2e/playwright.config.js --update-snapshots", "test:lighthouse": "lighthouse-ci autorun", "test:axe": "axe-cli --config .axe-config.json", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "test:validate": "npm run production:validate && npm run test:smoke", "production:validate": "node scripts/production-readiness-validation.js"}, "devDependencies": {"@playwright/test": "^1.40.0", "@axe-core/playwright": "^4.8.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.0", "lighthouse-ci": "^0.12.0", "axe-cli": "^4.8.0", "allure-playwright": "^2.9.0", "cross-env": "^7.0.3"}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"], "moduleNameMapping": {"\\.(css|less|scss|sass)$": "identity-obj-proxy"}, "collectCoverageFrom": ["src/**/*.{js,jsx}", "!src/**/*.test.{js,jsx}", "!src/index.js"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}, "lighthouse-ci": {"ci": {"collect": {"url": ["http://localhost:3000/", "http://localhost:3000/about", "http://localhost:3000/services", "http://localhost:3000/contact"], "startServerCommand": "npm run dev", "startServerReadyPattern": "ready on"}, "assert": {"assertions": {"categories:performance": ["warn", {"minScore": 0.85}], "categories:accessibility": ["error", {"minScore": 0.95}], "categories:best-practices": ["warn", {"minScore": 0.9}], "categories:seo": ["warn", {"minScore": 0.9}], "first-contentful-paint": ["warn", {"maxNumericValue": 2500}], "largest-contentful-paint": ["warn", {"maxNumericValue": 4000}], "cumulative-layout-shift": ["warn", {"maxNumericValue": 0.1}], "first-input-delay": ["warn", {"maxNumericValue": 100}]}}, "upload": {"target": "temporary-public-storage"}}}}