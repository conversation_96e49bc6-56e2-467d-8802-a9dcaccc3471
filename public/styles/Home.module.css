/* DigiClick AI Home Page Styles */

.container {
  min-height: 100vh;
  padding: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #121212 0%, #1a1a1a 100%);
}

.main {
  padding: 5rem 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.hero {
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.title {
  font-family: 'Orbitron', monospace;
  font-size: clamp(2rem, 5vw, 4rem);
  font-weight: 900;
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #00d4ff 0%, #7b2cbf 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
}

.subtitle {
  font-family: 'Poppins', sans-serif;
  font-size: clamp(1rem, 3vw, 1.5rem);
  color: #e0e0e0;
  margin-bottom: 3rem;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.ctaButton {
  background: linear-gradient(45deg, #00d4ff, #7b2cbf);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
  margin: 0 1rem 1rem 0;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.ctaButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.4);
}

.secondaryButton {
  background: transparent;
  color: #00d4ff;
  border: 2px solid #00d4ff;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin: 0 1rem 1rem 0;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.secondaryButton:hover {
  background: #00d4ff;
  color: #121212;
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 4rem;
  width: 100%;
  max-width: 1200px;
}

.featureCard {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.featureCard:hover {
  transform: translateY(-5px);
  border-color: #00d4ff;
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.2);
}

.featureIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #00d4ff;
}

.featureTitle {
  font-family: 'Orbitron', monospace;
  font-size: 1.5rem;
  font-weight: 700;
  color: #00d4ff;
  margin-bottom: 1rem;
}

.featureDescription {
  font-family: 'Poppins', sans-serif;
  color: #b0b0b0;
  line-height: 1.6;
}

.stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-top: 3rem;
  flex-wrap: wrap;
}

.stat {
  text-align: center;
}

.statNumber {
  font-family: 'Orbitron', monospace;
  font-size: 2.5rem;
  font-weight: 900;
  color: #00d4ff;
  display: block;
}

.statLabel {
  font-family: 'Poppins', sans-serif;
  color: #b0b0b0;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }
  
  .main {
    padding: 2rem 0;
  }
  
  .hero {
    padding: 0 1rem;
  }
  
  .features {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-top: 2rem;
  }
  
  .stats {
    gap: 2rem;
  }
  
  .ctaButton,
  .secondaryButton {
    width: 100%;
    margin: 0.5rem 0;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
  
  .featureCard {
    padding: 1.5rem;
  }
  
  .stats {
    flex-direction: column;
    gap: 1.5rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .ctaButton,
  .secondaryButton,
  .featureCard {
    transition: none;
  }
  
  .ctaButton:hover,
  .secondaryButton:hover,
  .featureCard:hover {
    transform: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .title {
    color: #ffffff;
    text-shadow: none;
  }
  
  .featureCard {
    background: #000000;
    border-color: #ffffff;
  }
  
  .featureIcon,
  .featureTitle {
    color: #ffffff;
  }
  
  .ctaButton {
    background: #ffffff;
    color: #000000;
  }
  
  .secondaryButton {
    border-color: #ffffff;
    color: #ffffff;
  }
}
