/* DigiClick AI Visual Effects CSS */

/* Glow Effects */
.glow-text {
  background: linear-gradient(45deg, #00d4ff, #7b2cbf);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

.glow-button {
  background: linear-gradient(45deg, #00d4ff, #7b2cbf);
  border: none;
  color: white;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

.glow-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.4);
}

/* Pulse Effects */
.pulse-box {
  animation: pulse 2s ease-in-out infinite;
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 10px;
  padding: 1rem;
  background: rgba(0, 212, 255, 0.05);
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
  }
}

/* Gradient Backgrounds */
.gradient-bg {
  background: linear-gradient(135deg, #121212 0%, #1a1a1a 50%, #2c2c2c 100%);
}

.gradient-border {
  border: 2px solid;
  border-image: linear-gradient(45deg, #00d4ff, #7b2cbf) 1;
}

/* Floating Animation */
.floating {
  animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Fade In Animations */
.fade-in {
  opacity: 0;
  animation: fadeIn 1s ease-in forwards;
}

.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 1s ease-out forwards;
}

.fade-in-left {
  opacity: 0;
  transform: translateX(-30px);
  animation: fadeInLeft 1s ease-out forwards;
}

.fade-in-right {
  opacity: 0;
  transform: translateX(30px);
  animation: fadeInRight 1s ease-out forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Hover Effects */
.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: all 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
  border-color: #00d4ff;
}

/* Loading Spinner */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 212, 255, 0.1);
  border-left: 4px solid #00d4ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .pulse-box,
  .floating,
  .fade-in,
  .fade-in-up,
  .fade-in-left,
  .fade-in-right,
  .spinner {
    animation: none;
  }
  
  .hover-scale:hover {
    transform: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .glow-text {
    color: #ffffff;
    text-shadow: none;
  }
  
  .glow-button {
    background: #ffffff;
    color: #000000;
    border: 2px solid #ffffff;
  }
  
  .pulse-box {
    border-color: #ffffff;
    background: transparent;
  }
}
