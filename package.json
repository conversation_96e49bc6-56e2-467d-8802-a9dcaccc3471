{"name": "digiclick-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3002", "prebuild": "echo 'Starting build...'", "build": "next build", "start": "next start -p 3002", "lint": "next lint", "lint:css": "stylelint '**/*.css'", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "test": "jest", "test:ci": "jest --ci --coverage --watchAll=false", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "node tests/e2e/cursor-e2e.js", "test:e2e:open": "cypress open", "generate-sitemap": "node Scripts/generate-sitemap.js", "api": "node api-server.js", "api:dev": "nodemon api-server.js", "deploy": "./Scripts/deploy.sh", "deploy:staging": "./Scripts/deploy.sh staging", "deploy:production": "./Scripts/deploy.sh production", "deploy:frontend": "./Scripts/deploy.sh production frontend", "deploy:backend": "./Scripts/deploy.sh production backend", "deploy:hotfix": "./Scripts/deploy.sh production all --skip-tests", "docker:build": "docker build -t digiclick-ai .", "docker:run": "docker run -p 3000:3000 digiclick-ai", "docker:dev": "docker-compose up --build", "docker:prod": "docker-compose -f docker-compose.prod.yml up --build", "health-check": "curl -f http://localhost:3000/api/health || exit 1", "setup": "node Scripts/setup-env.js", "setup:production": "NODE_ENV=production node Scripts/setup-env.js", "validate-env": "node -e \"require('./utils/env-validation').validateAndExit()\"", "precommit": "lint-staged", "prepare": "husky install", "test:cursor": "jest tests/cursor.test.js", "test:cursor:watch": "jest tests/cursor.test.js --watch", "test:layout": "jest tests/layout.test.js", "test:performance": "node tests/performance/cursor-performance.js", "test:accessibility": "node tests/accessibility/cursor-a11y.js", "test:cross-device": "node tests/cross-device/device-tests.js", "test:all": "npm run test && npm run test:performance && npm run test:e2e && npm run test:accessibility", "check:cursor": "node Scripts/check-cursor.js", "deploy:verify": "node Scripts/verify-deployment.js", "deploy:monitor": "node Scripts/deployment-monitor.js", "deploy:health": "curl -f $NEXT_PUBLIC_APP_URL/api/health-check", "performance:check": "npm run deploy:verify", "deploy:vercel": "node Scripts/deploy.js vercel", "deploy:netlify": "node Scripts/deploy.js netlify", "deploy:skip-tests": "node Scripts/deploy.js vercel --skip-tests", "build:analyze": "ANALYZE=true npm run build", "sitemap:generate": "node Scripts/generate-sitemap.js", "performance:test": "echo 'Performance testing temporarily disabled for deployment'", "cursor:demo": "echo 'Visit http://localhost:3000/cursor-demo to test cursor functionality'", "postbuild": "npm run sitemap:generate", "optimize": "node scripts/performance-optimizer.js", "bundle-analyze": "node scripts/bundle-analyzer.js", "perf-audit": "lighthouse http://localhost:3000 --output=json --output-path=./reports/lighthouse-audit.json", "perf-test": "npm run build && npm run perf-audit", "optimize-images": "node scripts/optimize-images.js", "test:performance:full": "npm run build && npm run optimize && npm run perf-audit", "analyze:bundle": "ANALYZE=true npm run build", "monitor:performance": "node scripts/continuous-monitoring.js", "accessibility:audit": "node scripts/accessibility-audit.js", "accessibility:test": "npm run test -- tests/accessibility/", "accessibility:full": "npm run accessibility:audit && npm run accessibility:test", "test:a11y": "npm run accessibility:full", "production:validate": "node scripts/production-readiness-validation.js", "production:ready": "npm run production:validate", "test:manual": "node scripts/manual-testing-helper.js validate", "test:manual:checklist": "node scripts/manual-testing-helper.js checklist", "test:deployment": "npm run production:validate && npm run test:manual", "test:deployment:quick": "npm run test:manual", "test:deployment:full": "npm run production:validate && npm run test:manual && npm run test:e2e"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hello-pangea/dnd": "^16.6.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@notionhq/client": "^2.3.0", "@sentry/tracing": "^8.0.0", "@stripe/stripe-js": "^7.2.0", "@testing-library/dom": "^10.4.0", "@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.21", "axios": "^1.3.6", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "critters": "^0.0.23", "cssnano": "^7.0.6", "csurf": "^1.11.0", "csv-parser": "^3.2.0", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.0.1", "firebase": "^11.6.1", "gsap": "^3.13.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "messagebird": "^4.0.1", "micro": "^10.0.1", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.2", "next": "^15.3.3", "next-pwa": "^5.6.0", "next-seo": "^6.4.0", "next-sitemap": "^4.2.3", "node-cron": "^3.0.2", "nodemailer": "^6.10.1", "particles.js": "^2.0.0", "postcss": "^8.5.3", "postcss-cli": "^11.0.1", "postcss-flexbugs-fixes": "^5.0.2", "postcss-import": "^16.1.0", "postcss-preset-env": "^10.1.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.11", "react-hook-form": "^7.48.2", "react-image-gallery": "^1.3.0", "react-loading-skeleton": "^3.3.1", "react-query": "^3.39.3", "react-toastify": "^9.1.3", "sharp": "^0.33.2", "stripe": "^18.0.0", "swr": "^2.2.4", "tailwindcss": "^4.1.5", "telegraf": "^4.16.3", "twilio": "^4.11.0", "typescript": "^5.3.3", "xss-clean": "^0.1.4", "yup": "^1.3.2", "zustand": "^4.4.7"}, "devDependencies": {"@axe-core/cli": "^4.10.1", "@cloudflare/workers-types": "^4.20250430.0", "@sentry/nextjs": "^8.0.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "axe-core": "^4.10.3", "babel-jest": "^29.7.0", "babel-plugin-transform-imports": "^2.0.0", "chrome-launcher": "^1.2.0", "cypress": "^13.17.0", "eslint": "^8.57.1", "eslint-config-next": "^14.0.4", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-react": "^7.33.2", "husky": "^8.0.3", "identity-obj-proxy": "^3.0.0", "imagemin": "^8.0.1", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^10.0.0", "imagemin-webp": "^8.0.0", "jest": "^29.7.0", "jest-axe": "^8.0.0", "jest-environment-jsdom": "^29.7.0", "jest-watch-typeahead": "^2.2.2", "lighthouse": "^12.6.1", "lint-staged": "^15.2.0", "msw": "^2.0.11", "nodemon": "^3.0.2", "prettier": "^3.1.1", "puppeteer": "^24.10.0", "styled-components": "^6.1.8", "stylelint": "^16.2.1", "stylelint-config-recommended": "^14.0.0", "stylelint-config-standard": "^36.0.0", "stylelint-order": "^6.0.4", "supertest": "^7.1.0", "web-vitals": "^3.5.2", "webpack-bundle-analyzer": "^4.10.1"}}